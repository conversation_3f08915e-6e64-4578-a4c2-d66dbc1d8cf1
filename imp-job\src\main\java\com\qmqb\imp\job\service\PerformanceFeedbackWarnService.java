package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.domain.TreeEntity;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.DeptTypeEnum;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.ReleaseRecord;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.mapper.ReleaseRecordMapper;
import com.qmqb.imp.system.mapper.SysDeptMapper;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceTutoringMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 绩效反馈通知
 * @date 2025/7/2 17:40
 */
@Component
@Slf4j
public class PerformanceFeedbackWarnService {

    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private IMessageService messageService;
    @Resource
    private PerformanceFeedbackMainMapper performanceFeedbackMainMapper;
    @Resource
    private PerformanceFeedbackMapper performanceFeedbackMapper;
    @Resource
    private PerformanceTutoringMapper performanceTutoringMapper;
    @Resource
    private ReleaseRecordMapper releaseRecordMapper;


    /**
     * 组长绩效反馈通知
     *
     * @param param
     * @return
     */
    @TraceId("组长绩效反馈通知")
    @XxlJob("leaderPerformanceFeedbackWarnJobHandler")
    public ReturnT<String> leaderPerformanceFeedbackWarnJobHandler(String param) {
        log.info("开始执行组长绩效反馈通知任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            Date nowDateStart = DateUtils.dateToStart(nowDate);
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            List<SysDept> deptList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
                .ne(SysDept::getDeptType, DeptTypeEnum.PM.getCode()));
            Map<Long, String> deptLeaderMap = deptList.stream()
                .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getLeader));
            List<String> leaderNameList = deptList.stream()
                .flatMap(sysDept -> Arrays.stream(sysDept.getLeader().split(",")).filter(StringUtils::isNotBlank))
                .collect(Collectors.toList());
            List<SysUser> leaderList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getNickName, leaderNameList));
            Map<String, String> leaderNickNameMap = leaderList.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));

            // 发送昨日及以前有未提交状态绩效反馈数据的组长名单
            sendNotSubmittedWarning(nowDateStart, leaderNickNameMap);
            // 连续5个工作日以上没有提交任何绩效反馈的组长名单
            sendInFiveWorkDayNotSubmittedFeedBackWarning(nowDateStart, leaderNickNameMap);
            // 超过2周只提交S或A指标，未提交C或D反馈的组长名单
            sendSubmitLevelAbnormalLeaderWarn(nowDateStart, leaderNickNameMap);
            // 有C或D绩效，超过3个工作日未填写绩效辅导的组长名单
            sendMoreThanThreeWorkDayNotTutoringLeaderList(nowDateStart, deptLeaderMap);
            log.info("组长绩效反馈通知任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("组长绩效反馈通知任务执行失败", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 项目管理绩效反馈通知
     *
     * @param param
     * @return
     */
    @TraceId("项目管理绩效反馈通知")
    @XxlJob("managerPerformanceFeedbackWarnJobHandler")
    public ReturnT<String> managerPerformanceFeedbackWarnJobHandler(String param) {
        log.info("开始执行项目管理绩效反馈通知任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            Date nowDateStart = DateUtils.dateToStart(nowDate);
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            List<Long> pmDeptIdList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                    .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
                    .eq(SysDept::getDeptType, DeptTypeEnum.PM.getCode()))
                .stream()
                .map(SysDept::getDeptId)
                .collect(Collectors.toList());
            Map<String, String> pmNickNameMap = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, pmDeptIdList))
                .stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));

            // 连续2个工作日没有新增的项目成果
            inTwoFiveWorkDayNotCreateRelease(nowDateStart, pmNickNameMap);
            // 连续5个工作日以上没有提交任何绩效反馈
            sendInFiveWorkDayNotCreateFeedBackWarning(nowDateStart, pmNickNameMap);
            // 超过2周只提交S或A指标，未提交C或D反馈的项目经理名单
            sendSubmitLevelAbnormalPmWarn(nowDateStart, pmNickNameMap);
            log.info("项目管理绩效反馈通知任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("项目管理绩效反馈通知任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param template 模板
     * @param map      数据
     */
    private void send(String robotUrl, String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

    /**
     * 发送昨日及以前有未提交状态绩效反馈数据的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendNotSubmittedWarning(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        List<String> yesterdayNotSubmittedLeaderList = new ArrayList<>();
        List<String> beforeNotSubmittedLeaderList = new ArrayList<>();
        if (!leaderNickNameMap.isEmpty()) {
            List<PerformanceFeedbackMain> notSubmittedList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                .in(PerformanceFeedbackMain::getCreateBy, leaderNickNameMap.keySet())
                .lt(BaseEntity::getCreateTime, nowDateStart)
                .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode()));
            yesterdayNotSubmittedLeaderList = notSubmittedList.stream()
                .filter(item -> !item.getCreateTime().before(DateUtils.plusDay(nowDateStart, -1)))
                .map(item -> leaderNickNameMap.getOrDefault(item.getCreateBy(), ""))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
            beforeNotSubmittedLeaderList = notSubmittedList.stream()
                .filter(item -> item.getCreateTime().before(DateUtils.plusDay(nowDateStart, -1)))
                .map(item -> leaderNickNameMap.getOrDefault(item.getCreateBy(), ""))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        }
        Map<String, String> map = new HashMap<>(16);
        map.put("yesterdayNotSubmitLeaders", CollectionUtils.isEmpty(yesterdayNotSubmittedLeaderList) ? "无" :
            StringUtils.join(yesterdayNotSubmittedLeaderList, "、"));
        map.put("beforeNotSubmitLeaders", CollectionUtils.isEmpty(beforeNotSubmittedLeaderList) ? "无" :
            StringUtils.join(beforeNotSubmittedLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK, map);
    }

    /**
     * 连续5个工作日以上没有提交任何绩效反馈的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendInFiveWorkDayNotSubmittedFeedBackWarning(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        Date beforeFiveWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 5);
        List<PerformanceFeedbackMain> inFiveWorkDaySumbitList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
            .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
            .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart)
            .ge(PerformanceFeedbackMain::getSubmitTime, beforeFiveWorkDay));
        List<String> inFiveWorkDaySumbitUserNameList = inFiveWorkDaySumbitList.stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getSubmitTime())))
            .map(PerformanceFeedbackMain::getSubmitter)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> notSubmitUserList = leaderNickNameMap.entrySet().stream()
            .filter(entry -> !inFiveWorkDaySumbitUserNameList.contains(entry.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("users", CollectionUtils.isEmpty(notSubmitUserList) ? "无" :
            StringUtils.join(notSubmitUserList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS, map);
    }

    /**
     * 超过2周只提交S或A指标，未提交C或D反馈的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendSubmitLevelAbnormalLeaderWarn(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        List<String> submitLevelAbnormalLeaderList = new ArrayList<>();
        if (!leaderNickNameMap.isEmpty()) {
            Date beforeTwoWeeks = DateUtils.plusDay(nowDateStart, -14);
            List<Long> inTwoWeeksSumbitFeedbackIdList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                    .select(PerformanceFeedbackMain::getId)
                    .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
                    .ge(PerformanceFeedbackMain::getSubmitTime, beforeTwoWeeks)
                    .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart))
                .stream().map(PerformanceFeedbackMain::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inTwoWeeksSumbitFeedbackIdList)) {
                Map<String, List<String>> levelMap = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                        .in(PerformanceFeedback::getMainFeedbackId, inTwoWeeksSumbitFeedbackIdList))
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCreateBy()))
                    .collect(Collectors.groupingBy(PerformanceFeedback::getCreateBy, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().map(PerformanceFeedback::getRecommendedLevel).collect(Collectors.toList()))));
                submitLevelAbnormalLeaderList = leaderNickNameMap.entrySet().stream().filter(entry -> {
                    List<String> levelList = levelMap.getOrDefault(entry.getKey(), Collections.emptyList());
                    if (CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode())
                        && !CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_C.getCode(), ScoreLevelEnum.SCORE_D.getCode())) {
                        return true;
                    }
                    return false;
                }).map(Map.Entry::getValue).collect(Collectors.toList());
            }
        }
        Map<String, String> map = Collections.singletonMap("leaders", CollectionUtils.isEmpty(submitLevelAbnormalLeaderList) ? "无" :
            StringUtils.join(submitLevelAbnormalLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS, map);
    }

    /**
     * 有C或D绩效，超过3个工作日未填写绩效辅导的组长名单
     *
     * @param nowDateStart
     * @param deptLeaderMap
     */
    private void sendMoreThanThreeWorkDayNotTutoringLeaderList(Date nowDateStart, Map<Long, String> deptLeaderMap) {
        List<String> moreThanThreeWorkDayNotTutoringLeaderList = new ArrayList<>();
        Date beforeThreeWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 3);
        List<Long> beforeThreeWorkDayNotTutoringFeedbackIdList = performanceTutoringMapper.selectList(new LambdaQueryWrapper<PerformanceTutoring>()
                .lt(BaseEntity::getCreateTime, beforeThreeWorkDay)
                .isNull(PerformanceTutoring::getTutoringTime))
            .stream().map(PerformanceTutoring::getFeedbackId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(beforeThreeWorkDayNotTutoringFeedbackIdList)) {
            List<Long> beforeThreeWorkDayNotTutoringGroupIdList = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                    .in(PerformanceFeedback::getId, beforeThreeWorkDayNotTutoringFeedbackIdList))
                .stream().map(PerformanceFeedback::getGroupId).collect(Collectors.toList());
            moreThanThreeWorkDayNotTutoringLeaderList = deptLeaderMap.entrySet()
                .stream()
                .filter(entry -> beforeThreeWorkDayNotTutoringGroupIdList.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        }
        Map<String, String> map = Collections.singletonMap("leaders", CollectionUtils.isEmpty(moreThanThreeWorkDayNotTutoringLeaderList) ? "无" :
            StringUtils.join(moreThanThreeWorkDayNotTutoringLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_COACHING, map);
    }

    /**
     * 连续2个工作日没有新增的项目成果
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void inTwoFiveWorkDayNotCreateRelease(Date nowDateStart, Map<String, String> pmNickNameMap) {
        Date beforeTwoWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 2);
        List<String> pmList = releaseRecordMapper.selectList(new LambdaQueryWrapper<ReleaseRecord>()
                .ge(ReleaseRecord::getCreatedTime, beforeTwoWorkDay)
                .lt(ReleaseRecord::getCreatedTime, nowDateStart))
            .stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getCreatedTime())))
            .map(ReleaseRecord::getProjectManager)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> inTwoFiveWorkDayNotCreateReleasePmList = pmNickNameMap.entrySet()
            .stream()
            .filter(entry -> !pmList.contains(entry.getKey()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(inTwoFiveWorkDayNotCreateReleasePmList) ? "无" :
            StringUtils.join(inTwoFiveWorkDayNotCreateReleasePmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.NOT_ADD_PROJECT_OUTCOMES_MORE_THAN_TWO_DAYS, map);
    }


    /**
     * 连续5个工作日以上没有提交任何绩效反馈
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void sendInFiveWorkDayNotCreateFeedBackWarning(Date nowDateStart, Map<String, String> pmNickNameMap) {

        Date beforeFiveWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 5);
        List<PerformanceFeedbackMain> inFiveWorkDaySumbitList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
            .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
            .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart)
            .ge(PerformanceFeedbackMain::getSubmitTime, beforeFiveWorkDay));
        List<String> inFiveWorkDaySumbitUserNameList = inFiveWorkDaySumbitList.stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getSubmitTime())))
            .map(PerformanceFeedbackMain::getSubmitter)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> notCreatePmList = pmNickNameMap.entrySet().stream()
            .filter(entry -> !inFiveWorkDaySumbitUserNameList.contains(entry.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(notCreatePmList) ? "无" :
            StringUtils.join(notCreatePmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS_MANAGER, map);
    }

    /**
     * 超过2周只提交S或A指标，未提交C或D反馈的项目经理名单
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void sendSubmitLevelAbnormalPmWarn(Date nowDateStart, Map<String, String> pmNickNameMap) {
        List<String> submitLevelAbnormalPmList = new ArrayList<>();
        if (!pmNickNameMap.isEmpty()) {
            Date beforeTwoWeeks = DateUtils.plusDay(nowDateStart, -14);
            List<Long> inTwoWeeksSumbitFeedbackIdList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                    .select(PerformanceFeedbackMain::getId)
                    .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
                    .ge(PerformanceFeedbackMain::getSubmitTime, beforeTwoWeeks)
                    .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart))
                .stream().map(PerformanceFeedbackMain::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inTwoWeeksSumbitFeedbackIdList)) {
                Map<String, List<String>> levelMap = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                        .in(PerformanceFeedback::getMainFeedbackId, inTwoWeeksSumbitFeedbackIdList))
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCreateBy()))
                    .collect(Collectors.groupingBy(PerformanceFeedback::getCreateBy, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().map(PerformanceFeedback::getRecommendedLevel).collect(Collectors.toList()))));
                submitLevelAbnormalPmList = pmNickNameMap.entrySet().stream().filter(entry -> {
                    List<String> levelList = levelMap.getOrDefault(entry.getKey(), Collections.emptyList());
                    return CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode())
                        && !CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_C.getCode(), ScoreLevelEnum.SCORE_D.getCode());
                }).map(Map.Entry::getValue).collect(Collectors.toList());
            }
        }
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(submitLevelAbnormalPmList) ? "无" :
            StringUtils.join(submitLevelAbnormalPmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS_MANAGER, map);
    }


}
