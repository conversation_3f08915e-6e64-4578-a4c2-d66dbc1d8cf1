package com.qmqb.imp.common.core.domain.dto;

import com.qmqb.imp.common.core.domain.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */
@Data
@Schema(description = "绩效综合统计查询请求参数")
public class PerfStatQueryDTO  extends PageQuery {

    /**
     * 人员类型:1-全部,2-技术经理,3-开发人员,4-测试人员,5-非组长
     */
    @Schema(description = "人员类型")
    private Integer personType;

    @Schema(description = "所属组别ID")
    private Long groupId;

    @Schema(description = "完成年份")
    private Integer year;

    @Schema(description = "完成月份")
    private Integer month;

    @Schema(description = "最优查询:0-否,1-是")
    private Integer bestQuery;

}
