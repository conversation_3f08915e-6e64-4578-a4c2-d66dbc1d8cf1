package com.qmqb.imp.system.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;


/**
 * 绩效事件视图对象 tb_performance_event
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceEventVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 反馈编码
     */
    @ExcelProperty(value = "反馈编码")
    private String feedbackCode;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer year;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private Integer month;

    /**
     * 反馈时间
     */
    @ExcelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 事件标题
     */
    @ExcelProperty(value = "事件标题")
    private String eventTitle;

    /**
     * 事件明细
     */
    @ExcelProperty(value = "事件明细")
    private String eventDetail;

    /**
     * 事件发生时间-开始
     */
    @ExcelProperty(value = "事件发生时间-开始")
    private Date eventStartTime;

    /**
     * 事件发生时间-结束
     */
    @ExcelProperty(value = "事件发生时间-结束")
    private Date eventEndTime;

    /**
     * 绩效分布情况，格式如：S：20人\nA：30人\nC：1人\nD：1人
     */
    @ExcelProperty(value = "绩效分布情况")
    private String performanceDistribution;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String dataSource;

    /**
     * 提交状态
     */
    @ExcelProperty(value = "提交状态")
    private String submitStatus;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 提交人
     */
    @ExcelProperty(value = "提交人")
    private String submitter;

    /**
     * 提交人
     */
    @ExcelProperty(value = "提交人名称")
    private String submitterName;

    /**
     * 项管审核状态
     */
    @ExcelProperty(value = "项管审核状态")
    private String projectManagerAuditStatus;
    /**
     * 项管审核人
     */
    @ExcelProperty(value = "项管审核人")
    private String projectManagerAuditor;
    /**
     * 最终审核状态
     */
    @ExcelProperty(value = "最终审核状态")
    private String finalAudit;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 反馈明细列表
     */
    @Valid
    private List<PerformanceFeedbackVo> performanceFeedbackList;



}
