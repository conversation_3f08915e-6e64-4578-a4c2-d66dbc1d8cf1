package com.qmqb.imp.system.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 慢SQL处理状态
 * @date 2025/5/28 14:28
 */
@Getter
public enum SlowQueryProcessStatusEnum {

    /**
     * 未指派
     */
    NOT_ASSIGN("0", "未指派"),

    /**
     * 已指派未处理
     */
    ASSIGN_NOT_PROCESS("1", "已指派未处理"),
    /**
     * 已处理
     */
    ASSIGN_PROCESS("2", "已指派已处理"),
    ;

    /**
     * 状态码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    SlowQueryProcessStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
