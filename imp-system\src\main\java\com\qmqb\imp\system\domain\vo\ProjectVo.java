package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;


/**
 * 项目管理视图对象 tb_project
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectVo {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    @JsonProperty("pId")
    private Long pId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    @JsonProperty("pName")
    private String pName;

    /**
     * 项目路径
     */
    @ExcelProperty(value = "项目路径")
    @JsonProperty("pNamespace")
    private String pNamespace;

    /**
     * 创建日期
     */
    @ExcelProperty(value = "创建日期")
    @JsonProperty("pCreatetime")
    private Date pCreatetime;

    /**
     * 最后更新日期
     */
    @ExcelProperty(value = "最后更新日期")
    @JsonProperty("pLastUpdateTime")
    private Date pLastUpdateTime;

    /**
     * 项目描述
     */
    @ExcelProperty(value = "项目描述")
    @JsonProperty("pDesc")
    private String pDesc;

    /**
     * web访问地址
     */
    @ExcelProperty(value = "web访问地址")
    @JsonProperty("pWebUrl")
    private String pWebUrl;

    /**
     * 可视性
     */
    @ExcelProperty(value = "可视性")
    @JsonProperty("pVisibility")
    private String pVisibility;

    /**
     * 入库时间
     */
    @ExcelProperty(value = "入库时间")
    @JsonProperty("pSyncDatetime")
    private Date pSyncDatetime;

    /**
     * 负责开发组id
     */
    @ExcelProperty(value = "负责开发组id")
    @JsonProperty("pDevDept")
    private String pDevDept;

    /**
     * 负责测试组id
     */
    @ExcelProperty(value = "负责测试组id")
    @JsonProperty("pTestDept")
    private String pTestDept;

    /**
     * 大类业务id
     */
    @ExcelProperty(value = "大类业务id")
    @JsonProperty("pBroadBusiness")
    private Long pBroadBusiness;

    /**
     * 小类业务id
     */
    @ExcelProperty(value = "小类业务id")
    @JsonProperty("pNarrowBusiness")
    private Long pNarrowBusiness;
    /**
     * 负责开发组名称
     */
    @ExcelProperty(value = "负责开发组")
    @JsonProperty("pDevDeptName")
    private String pDevDeptName;

    /**
     * 负责测试组名称
     */
    @ExcelProperty(value = "负责测试组")
    @JsonProperty("pTestDeptName")
    private String pTestDeptName;

    /**
     * 大类业务名称
     */
    @ExcelProperty(value = "大类业务")
    @JsonProperty("pBroadBusinessName")
    private String pBroadBusinessName;

    /**
     * 小类业务名称
     */
    @ExcelProperty(value = "小类业务")
    @JsonProperty("pNarrowBusinessName")
    private String pNarrowBusinessName;

    /**
     * 分支数
     */
    @ExcelProperty(value = "分支数")
    @JsonProperty("pBranchCount")
    private Long pBranchCount;

    /**
     * 是否有master分支(0无，1有)
     */
    @ExcelProperty(value = "是否有master分支")
    @JsonProperty("pHasMaster")
    private Integer pHasMaster;

    /**
     * master分支是否受保护(0无，1有)
     */
    @ExcelProperty(value = "master分支是否受保护")
    @JsonProperty("pMasterProtected")
    private Integer pMasterProtected;
}
