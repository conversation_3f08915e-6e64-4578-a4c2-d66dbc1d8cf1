package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 项目管理对象 tb_project
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
@TableName("tb_project")
public class Project {

    private static final long serialVersionUID=1L;

    /**
     * 项目ID
     */
    @TableId(value = "p_id")
    private Long pId;
    /**
     * 项目名称
     */
    private String pName;
    /**
     * 项目路径
     */
    private String pNamespace;
    /**
     * 创建日期
     */
    private Date pCreatetime;
    /**
     * 最后更新日期
     */
    private Date pLastUpdateTime;
    /**
     * 项目描述
     */
    private String pDesc;
    /**
     * web访问地址
     */
    private String pWebUrl;
    /**
     * 可视性
     */
    private String pVisibility;
    /**
     * 入库时间
     */
    private Date pSyncDatetime;
    /**
     * 负责开发组
     */
    private String pDevDept;
    /**
     * 负责测试组
     */
    private String pTestDept;
    /**
     * 大类业务
     */
    private Long pBroadBusiness;
    /**
     * 小类业务
     */
    private Long pNarrowBusiness;
    /**
     * 分支数
     */
    private Long pBranchCount;

    /**
     * 是否有master分支(0无，1有)
     */
    private Integer pHasMaster;

    /**
     * master分支是否受保护(0无，1有)
     */
    private Integer pMasterProtected;
}
