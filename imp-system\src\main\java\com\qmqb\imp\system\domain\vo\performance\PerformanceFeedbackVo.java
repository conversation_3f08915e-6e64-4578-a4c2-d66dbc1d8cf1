package com.qmqb.imp.system.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 绩效反馈视图对象 tb_performance_feedback
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceFeedbackVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 主反馈表ID（关联主反馈表）
     */
    @ExcelProperty(value = "主反馈表ID")
    private Long mainFeedbackId;

    /**
     * 所属组ID
     */
    @ExcelProperty(value = "所属组ID")
    private Long groupId;

    /**
     * 所属组
     */
    @ExcelProperty(value = "所属组")
    private String groupName;

    /**
     * 反馈编码
     */
    @ExcelProperty(value = "反馈编码")
    private String feedbackCode;

    /**
     * 反馈时间
     */
    @ExcelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 一类指标
     */
    @ExcelProperty(value = "一类指标")
    private String primaryIndicator;

    /**
     * 二类指标
     */
    @ExcelProperty(value = "二类指标")
    private String secondaryIndicator;

    /**
     * 事件标题
     */
    @ExcelProperty(value = "事件标题")
    private String eventTitle;

    /**
     * 事件明细
     */
    @ExcelProperty(value = "事件明细")
    private String eventDetail;

    /**
     * 事件发生开始时间
     */
    @ExcelProperty(value = "事件发生开始时间")
    private Date eventStartTime;

    /**
     * 事件发生结束时间
     */
    @ExcelProperty(value = "事件发生结束时间")
    private Date eventEndTime;

    /**
     * 推荐绩效级别
     */
    @ExcelProperty(value = "推荐绩效级别")
    private String recommendedLevel;

    /**
     * 推荐原因
     */
    @ExcelProperty(value = "推荐原因")
    private String recommendedReason;


    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String dataSource;

    /**
     * 员工昵称
     */
    @ExcelProperty(value = "员工昵称")
    private String nickName;

    /**
     * 绩效年份
     */
    @ExcelProperty(value = "绩效年份")
    private Integer year;

    /**
     * 绩效月份
     */
    @ExcelProperty(value = "绩效月份")
    private Integer month;

    /**
     * 角色类型
     */
    @ExcelProperty(value = "角色类型")
    private String personType;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 提交人
     */
    @ExcelProperty(value = "提交人")
    private String submitter;

    /**
     * 项管审核状态
     */
    @ExcelProperty(value = "项管审核状态")
    private String projectManagerAuditStatus;

    /**
     * 项管审核人
     */
    @ExcelProperty(value = "项管审核人")
    private String projectManagerAuditor;

    /**
     * 项管审核时间
     */
    @ExcelProperty(value = "项管审核时间")
    private Date projectManagerAuditTime;

    /**
     * 项管审核备注
     */
    @ExcelProperty(value = "项管审核备注")
    private String projectManagerRemark;

    /**
     * 最终审核状态
     */
    @ExcelProperty(value = "最终审核状态")
    private String finalAudit;

    /**
     * 最终审核时间
     */
    @ExcelProperty(value = "最终审核时间")
    private Date finalAuditTime;

    /**
     * 最终审核备注
     */
    @ExcelProperty(value = "最终审核备注")
    private String finalRemark;

    /**
     * 绩效指标结果ID列表
     */
    private List<Long> indicatorResultIds;
}
