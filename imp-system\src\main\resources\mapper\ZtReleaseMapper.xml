<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtReleaseMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtRelease" id="ZtReleaseResult">
        <result property="id" column="id"/>
        <result property="project" column="project"/>
        <result property="product" column="product"/>
        <result property="branch" column="branch"/>
        <result property="shadow" column="shadow"/>
        <result property="build" column="build"/>
        <result property="name" column="name"/>
        <result property="system" column="system"/>
        <result property="releases" column="releases"/>
        <result property="marker" column="marker"/>
        <result property="date" column="date"/>
        <result property="releasedDate" column="releasedDate"/>
        <result property="stories" column="stories"/>
        <result property="bugs" column="bugs"/>
        <result property="leftBugs" column="leftBugs"/>
        <result property="desc" column="desc"/>
        <result property="mailto" column="mailto"/>
        <result property="notify" column="notify"/>
        <result property="status" column="status"/>
        <result property="subStatus" column="subStatus"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createdDate" column="createdDate"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <!-- 查询需要同步的发布记录，获取所有发布记录（包括已删除的） -->
    <select id="selectSyncReleaseList" resultMap="ZtReleaseResult">
        SELECT r.id, r.project, r.product, r.branch, r.shadow, r.build, r.name,
               r.`system`, r.releases, r.marker, r.date, r.releasedDate, r.stories,
               r.bugs, r.leftBugs, r.`desc`, r.mailto, r.notify, r.status, r.subStatus,
               r.createdBy, r.createdDate, r.deleted
        FROM zt_release r
        ORDER BY r.id
    </select>

    <!-- 查询需要同步的发布记录（按创建人过滤），获取指定创建人的发布记录（包括已删除的） -->
    <select id="selectSyncReleaseListByCreators" resultMap="ZtReleaseResult">
        SELECT r.id, r.project, r.product, r.branch, r.shadow, r.build, r.name,
               r.`system`, r.releases, r.marker, r.date, r.releasedDate, r.stories,
               r.bugs, r.leftBugs, r.`desc`, r.mailto, r.notify, r.status, r.subStatus,
               r.createdBy, r.createdDate, r.deleted
        FROM zt_release r
        WHERE r.createdBy IN
        <foreach collection="creators" item="creator" open="(" separator="," close=")">
            #{creator}
        </foreach>
        ORDER BY r.id
    </select>

    <!-- 根据更新时间查询有变动的发布记录ID -->
    <select id="selectChangedReleaseIds" resultType="java.lang.Integer">
        SELECT DISTINCT a.objectID
        FROM zt_action a
        WHERE a.objectType = 'release'
          AND a.action IN ('opened', 'edited', 'changestatus', 'published')
          AND a.date > #{afterTime}
        ORDER BY a.objectID
    </select>

    <!-- 根据更新时间和创建人查询有变动的发布记录ID -->
    <select id="selectChangedReleaseIdsByCreators" resultType="java.lang.Integer">
        SELECT DISTINCT a.objectID
        FROM zt_action a
        INNER JOIN zt_release r ON a.objectID = r.id
        WHERE a.objectType = 'release'
          AND a.action IN ('opened', 'edited', 'changestatus', 'published')
          AND a.date > #{afterTime}
          AND r.createdBy IN
        <foreach collection="creators" item="creator" open="(" separator="," close=")">
            #{creator}
        </foreach>
        ORDER BY a.objectID
    </select>

    <!-- 查询删除的发布记录ID -->
    <select id="selectDeletedReleaseIds" resultType="java.lang.Integer">
        SELECT DISTINCT a.objectID
        FROM zt_action a
        WHERE a.objectType = 'release'
          AND a.action = 'deleted'
          AND a.date > #{afterTime}
        ORDER BY a.objectID
    </select>

    <!-- 根据更新时间和创建人查询删除的发布记录ID -->
    <select id="selectDeletedReleaseIdsByCreators" resultType="java.lang.Integer">
        SELECT DISTINCT a.objectID
        FROM zt_action a
        INNER JOIN zt_release r ON a.objectID = r.id
        WHERE a.objectType = 'release'
          AND a.action = 'deleted'
          AND a.date > #{afterTime}
          AND r.createdBy IN
        <foreach collection="creators" item="creator" open="(" separator="," close=")">
            #{creator}
        </foreach>
        ORDER BY a.objectID
    </select>

    <!-- 根据ID列表查询发布记录 -->
    <select id="selectByIds" resultMap="ZtReleaseResult">
        SELECT r.id, r.project, r.product, r.branch, r.shadow, r.build, r.name,
               r.`system`, r.releases, r.marker, r.date, r.releasedDate, r.stories,
               r.bugs, r.leftBugs, r.`desc`, r.mailto, r.notify, r.status, r.subStatus,
               r.createdBy, r.createdDate, r.deleted
        FROM zt_release r
        WHERE r.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY r.id
    </select>

</mapper> 