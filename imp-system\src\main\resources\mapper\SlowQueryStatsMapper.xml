<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowQueryStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.imp.system.domain.SlowQueryStats">
        <id column="id" property="id" />
        <result column="DBName" property="dbName" />
        <result column="month" property="month" />
        <result column="SQLHash" property="sqlHash" />
        <result column="SQLText" property="sqlText" />
        <result column="total_query_times" property="totalQueryTimes" />
        <result column="total_sum_time" property="totalSumTime" />
        <result column="avg_query_time" property="avgQueryTime" />
        <result column="avg_returnRowCounts" property="avgReturnrowcounts" />
        <result column="avg_parseRowCounts" property="avgParserowcounts" />
        <result column="slow_rule" property="slowRule" />
        <result column="warn_level" property="warnLevel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, DBName, month, SQLHash, SQLText, total_query_times, total_sum_time, avg_query_time, avg_returnRowCounts, avg_parseRowCounts, slow_rule, warn_level
    </sql>

    <select id="statSlowSqlCount" resultType="com.qmqb.imp.system.domain.vo.SlowSqlCountVO">
        SELECT
            DBName AS dbName,
            LEFT(month, 4) AS year,
            RIGHT(month, 2) AS month,
            COUNT(DISTINCT SQLHash) AS slowSqlCount
        FROM
            t_slow_query_stats
        WHERE
            LEFT(month, 4) = #{year}
          <if test="status != null and status != -1">
              and `status` = #{status}
          </if>
        GROUP BY
            RIGHT(month, 2), DBName
        ORDER BY
            RIGHT(month, 2)
    </select>

</mapper>
