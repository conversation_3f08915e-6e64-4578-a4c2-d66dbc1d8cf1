<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceMapper">

    <resultMap type="com.qmqb.imp.system.domain.performance.Performance" id="PerformanceResult">
        <result property="id" column="id"/>
        <result property="nickName" column="nick_name"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="roleId" column="role_id"/>
        <result property="role" column="role"/>
        <result property="totalLevel" column="total_level"/>
        <result property="reviewLevel" column="review_level"/>
        <result property="finalLevel" column="final_level"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="emailSentFlag" column="email_sent_flag"/>
        <result property="emailSentTime" column="email_sent_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="PerformanceSummaryResultMap" type="com.qmqb.imp.system.domain.dto.PerformanceDTO">
        <result property="id" column="id"/>
        <result property="nickName" column="nick_name"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="roleId" column="role_id"/>
        <result property="role" column="role"/>
        <result property="totalLevel" column="total_level"/>
        <result property="reviewLevel" column="review_level"/>
        <result property="finalLevel" column="final_level"/>
        <result property="approvalTime" column="approval_time"/>
        <result property="emailSentFlag" column="email_sent_flag" javaType="java.lang.String"/>
        <result property="emailSentTime" column="email_sent_time" javaType="java.util.Date"/>
        <result property="remark" column="remark"/>
        <result property="workAchievementSummary" column="work_achievement_summary"/>
        <result property="workQualitySummary" column="work_quality_summary"/>
        <result property="collaborationAbilitySummary" column="collaboration_ability_summary"/>
        <result property="workAchievement" column="work_achievement"/>
        <result property="workQuality" column="work_quality"/>
        <result property="collaborationAbility" column="collaboration_ability"/>
        <result property="workAchievementSort" column="work_achievement_sort"/>
        <result property="workQualitySort" column="work_quality_sort"/>
        <result property="collaborationAbilitySort" column="collaboration_ability_sort"/>
        <result property="totalLevelSort" column="total_level_sort"/>
        <result property="reviewLevelSort" column="review_level_sort"/>
        <result property="finalLevelSort" column="final_level_sort"/>

        <!-- 其他字段映射 -->
    </resultMap>

    <select id="selectPerformanceSummary" resultMap="PerformanceSummaryResultMap">
        SELECT
        p.id AS `id`,
        p.nick_name AS `nick_name`,
        p.year AS `year`,
        p.month AS `month`,
        p.group_id AS `group_id`,
        p.group_name AS `group_name`,
        p.role_id AS `role_id`,
        p.role AS `role`,
        p.total_level AS `total_level`,
        p.review_level AS `review_level`,
        p.final_level AS `final_level`,
        p.approval_time AS `approval_time`,
        p.email_sent_flag AS `email_sent_flag`,
        p.email_sent_time AS `email_sent_time`,
        p.remark AS `remark`,

        -- 拼接文本（空值 → B）
        IFNULL(t.work_achievement_summary, 'B') AS `work_achievement_summary`,
        IFNULL(t.work_quality_summary,     'B') AS `work_quality_summary`,
        IFNULL(t.collaboration_ability_summary, 'B') AS `collaboration_ability_summary`,

        -- 新的排序字段：基于 tb_performance_indicator_category.category_level
        CASE
        WHEN MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END) = 'S' THEN 5
        WHEN MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END) = 'A' THEN 4
        WHEN MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END) = 'B' THEN 3
        WHEN MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END) = 'C' THEN 2
        WHEN MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END) = 'D' THEN 1
        ELSE 0
        END AS `work_achievement_sort`,

        CASE
        WHEN MAX(CASE WHEN c.category_code = 'work_quality' THEN c.category_level END) = 'S' THEN 5
        WHEN MAX(CASE WHEN c.category_code = 'work_quality' THEN c.category_level END) = 'A' THEN 4
        WHEN MAX(CASE WHEN c.category_code = 'work_quality' THEN c.category_level END) = 'B' THEN 3
        WHEN MAX(CASE WHEN c.category_code = 'work_quality' THEN c.category_level END) = 'C' THEN 2
        WHEN MAX(CASE WHEN c.category_code = 'work_quality' THEN c.category_level END) = 'D' THEN 1
        ELSE 0
        END AS `work_quality_sort`,

        CASE
        WHEN MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END) = 'S' THEN 5
        WHEN MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END) = 'A' THEN 4
        WHEN MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END) = 'B' THEN 3
        WHEN MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END) = 'C' THEN 2
        WHEN MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END) = 'D' THEN 1
        ELSE 0
        END AS `collaboration_ability_sort`,


        IFNULL(MAX(CASE WHEN c.category_code = 'work_achievement' THEN c.category_level END), 'B') AS `work_achievement`,
        IFNULL(MAX(CASE WHEN c.category_code = 'work_quality'     THEN c.category_level END), 'B') AS `work_quality`,
        IFNULL(MAX(CASE WHEN c.category_code = 'collaboration_ability' THEN c.category_level END), 'B') AS `collaboration_ability`,

        CASE
        WHEN p.total_level = 'S' THEN 5
        WHEN p.total_level = 'A' THEN 4
        WHEN p.total_level = 'B' THEN 3
        WHEN p.total_level = 'C' THEN 2
        WHEN p.total_level = 'D' THEN 1
        ELSE 0
        END AS `total_level_sort`,

        CASE
        WHEN p.review_level = 'S' THEN 5
        WHEN p.review_level = 'A' THEN 4
        WHEN p.review_level = 'B' THEN 3
        WHEN p.review_level = 'C' THEN 2
        WHEN p.review_level = 'D' THEN 1
        ELSE 0
        END AS `review_level_sort`,

        CASE
        WHEN p.final_level = 'S' THEN 5
        WHEN p.final_level = 'A' THEN 4
        WHEN p.final_level = 'B' THEN 3
        WHEN p.final_level = 'C' THEN 2
        WHEN p.final_level = 'D' THEN 1
        ELSE 0
        END AS `final_level_sort`

        FROM tb_performance p
        LEFT JOIN (
        SELECT
        performance_id,
        MAX(CASE WHEN category_code = 'work_achievement' THEN summary END) AS `work_achievement_summary`,
        MAX(CASE WHEN category_code = 'work_quality' THEN summary END) AS `work_quality_summary`,
        MAX(CASE WHEN category_code = 'collaboration_ability' THEN summary END) AS `collaboration_ability_summary`
        FROM (
        SELECT
        performance_id,
        category_code,
        GROUP_CONCAT(
        CASE
        WHEN cnt > 1 THEN CONCAT(cnt, score_level)
        ELSE score_level
        END
        ORDER BY FIELD(score_level, 'S', 'A', 'B', 'C', 'D')
        SEPARATOR '/'
        ) AS summary
        FROM (
        SELECT
        performance_id,
        category_code,
        score_level,
        COUNT(*) AS cnt
        FROM tb_performance_indicator
        GROUP BY performance_id, category_code, score_level
        ) x
        GROUP BY performance_id, category_code
        ) y
        GROUP BY performance_id
        ) t ON p.id = t.performance_id

        LEFT JOIN tb_performance_indicator_category c ON p.id = c.performance_id
        <where>
            <if test="performanceBo.year != null">
                AND p.year = #{performanceBo.year}
            </if>
            <if test="performanceBo.month != null and performanceBo.month != -1">
                AND p.month = #{performanceBo.month}
            </if>
            <if test="performanceBo.groupId != null">
                AND p.group_id = #{performanceBo.groupId}
            </if>
            <if test="performanceBo.roleId != null and performanceBo.roleId != 5 and performanceBo.roleId != 1 ">
                AND p.role_id = #{performanceBo.roleId}
            </if>
            <if test="performanceBo.nickName != null and performanceBo.nickName != ''">
                AND p.nick_name LIKE CONCAT('%', #{performanceBo.nickName}, '%')
            </if>
            <if test="performanceBo.totalLevel != null and performanceBo.totalLevel != ''">
                AND p.total_level = #{performanceBo.totalLevel}
            </if>
            <if test="performanceBo.reviewLevel != null and performanceBo.reviewLevel != ''">
                AND p.review_level = #{performanceBo.reviewLevel}
            </if>
            <if test="performanceBo.finalLevel != null and performanceBo.finalLevel != ''">
                AND p.final_level = #{performanceBo.finalLevel}
            </if>
            <if test="performanceBo.emailSentFlag != null and performanceBo.emailSentFlag != ''">
                AND p.email_sent_flag = #{performanceBo.emailSentFlag}
            </if>
            <if test="performanceBo.roleId == 5">AND p.role_id IN ('3', '4', '7')</if>
        </where>
        GROUP BY
        p.id,
        p.nick_name,
        p.year,
        p.month

    </select>

</mapper>
