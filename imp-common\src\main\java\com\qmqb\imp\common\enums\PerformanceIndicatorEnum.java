package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * 绩效考核指标枚举
 * <p>
 * 枚举了绩效考核中涉及的各项指标，包括代码行数、文档、任务、预警、故障、交付时效、规范、评审会、效率、服从、风险处理、制度遵守、团队协作等。
 * 每个枚举值包含一个英文编码（code）、中文名称（name）以及是否需要项管审核（needProjectManagementReview）。
 * </p>
 *
 * <ul>
 *   <li>{@link #CODE_LINES} 月新增代码行</li>
 *   <li>{@link #WORK_DOCS} 月新增工作相关文档</li>
 *   <li>{@link #ZENTAO_TASKS} 月禅道完成任务数</li>
 *   <li>{@link #PERFORMANCE_WARN} 月绩效预警数</li>
 *   <li>{@link #PROD_FAULT} 月生产故障数</li>
 *   <li>{@link #DELIVERY_TIMELINESS} 功能交付时效情况</li>
 *   <li>{@link #DEV_SPEC} 开发规范遵守情况</li>
 *   <li>{@link #REVIEW_MEETING} 参加评审会情况</li>
 *   <li>{@link #DEV_EFFICIENCY} 开发效率情况</li>
 *   <li>{@link #WORK_OBEY} 服从工作安排情况</li>
 *   <li>{@link #RISK_HANDLE} 及时处理风险情况</li>
 *   <li>{@link #WORK_RULES} 遵守工作制度情况</li>
 *   <li>{@link #TEAMWORK} 同事协作情况</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Getter
public enum PerformanceIndicatorEnum {
    /**
     * 月新增代码行
     */
    CODE_LINES("code_lines", "月新增代码行", false, true),
    /**
     * 月新增工作相关文档
     */
    WORK_DOCS("work_docs", "月新增工作相关文档", false, true),
    /**
     * 月禅道完成任务数
     */
    ZENTAO_TASKS("zentao_tasks", "月禅道完成任务数", false, true),
    /**
     * 月绩效预警数
     */
    PERFORMANCE_WARN("performance_warn", "月绩效预警数", false, true),
    /**
     * 月生产故障数
     */
    PROD_FAULT("prod_fault", "月生产故障数", true, true),
    /**
     * 功能交付时效情况
     */
    DELIVERY_TIMELINESS("delivery_timeliness", "功能交付时效情况", false, false),
    /**
     * 开发规范遵守情况
     */
    DEV_SPEC("dev_spec", "工作规范遵守情况", true, false),
    /**
     * 参加评审会情况
     */
    REVIEW_MEETING("review_meeting", "参加评审会情况", false, false),
    /**
     * 开发效率情况
     */
    DEV_EFFICIENCY("dev_efficiency", "工作效率情况", false, false),
    /**
     * 服从工作安排情况
     */
    WORK_OBEY("work_obey", "服从工作安排情况", false, false),
    /**
     * 及时处理风险情况
     */
    RISK_HANDLE("risk_handle", "及时处理风险情况", false, false),
    /**
     * 遵守工作制度情况
     */
    WORK_RULES("work_rules", "遵守工作制度情况", false, false),
    /**
     * 同事协作情况
     */
    TEAMWORK("teamwork", "同事协作情况", false, false),
    /**
     * 考勤指标
     */
    ATTENDANCE("attendance", "考勤指标", false, true),

    /**
     * 项目交付时效情况
     */
    PROJECT_DELIVERY_TIMELINESS("project_delivery_timeliness", "项目交付时效情况", true, false),

    /**
     * 月创建BUG数
     */
    BUG_COUNT("bug_count", "月创建BUG数", false, true),
    /**
     * 月执行用例数
     */
    CASE_EXECUTE_COUNT("case_execute_count", "月执行用例数", false, true),
    /**
     * 月计划性维护数
     */
    PLAN_MAINTAIN_COUNT("plan_maintain_count", "月计划性维护数", false, true),
    /**
     * 项目成功发布数
     */
    PROJECT_RELEASE_COUNT("project_release_count", "项目成功发布数", true, true),
    /**
     * 生产安全管理
     */
    PROD_SECURITY_MANAGE("prod_security_manage", "生产安全管理", false, false),
    /**
     * 生产操作管理
     */
    PROD_OPERATION_MANAGE("prod_operation_manage", "生产操作管理", false, false),
    /**
     * 参加会议情况
     */
    MEETING_ATTENDANCE("meeting_attendance", "参加会议情况", false, false),
    /**
     * 项目资源管理
     */
    RESOURCE_MANAGE("resource_manage", "项目资源管理", false, false),
    /**
     * 本组组员平均月考勤指标
     */
    GROUP_AVG_ATTENDANCE("group_avg_attendance", "本组组员平均月考勤指标", false, true),
    /**
     * 月结果产出
     */
    GROUP_RESULT_OUTPUT("group_result_output", "月结果产出", false, true),
    /**
     * 组织会议情况
     */
    ORGANIZATIONAL_MEETINGS("organizational_meetings", "组织会议情况", false, false),
    /**
     * 本组组员平均月新增工作相关文档
     */
    GROUP_AVG_WORK_DOCS("group_avg_work_docs", "本组组员平均月新增工作相关文档", false, true),
    /**
     * 本组组员月平均禅道完成任务数
     */
    GROUP_AVG_ZENTAO_TASKS("group_avg_zentao_tasks", "本组组员月平均禅道完成任务数", false, true),
    /**
     * 组月绩效预警数
     */
    GROUP_PERFORMANCE_WARN("group_performance_warn", "组月绩效预警数", false, true),
    /**
     * 工作效率管理
     */
    WORK_EFFICIENCY_MANAGE("work_efficiency_manage", "工作效率管理", false, false);

    /**
     * 指标编码
     */
    private final String code;
    /**
     * 指标名称
     */
    private final String name;
    /**
     * 是否需要项管审核
     */
    private final boolean needProjectManagementReview;
    /**
     * 是否系统生成
     */
    private final boolean isSystemGenerated;

    /**
     * 构造方法
     *
     * @param code 指标编码
     * @param name 指标名称
     * @param needProjectManagementReview 是否需要项管审核
     * @param isSystemGenerated 是否系统生成
     */
    PerformanceIndicatorEnum(String code, String name, boolean needProjectManagementReview, boolean isSystemGenerated) {
        this.code = code;
        this.name = name;
        this.needProjectManagementReview = needProjectManagementReview;
        this.isSystemGenerated = isSystemGenerated;
    }

    public boolean isSystemGenerated() {
        return isSystemGenerated;
    }

    /**
     * 根据 code 获取对应的枚举实例
     *
     * @param code 指标编码
     * @return 对应的 PerformanceIndicatorEnum 实例，如果未找到则返回 null
     */
    public static PerformanceIndicatorEnum fromCode(String code) {
        for (PerformanceIndicatorEnum indicator : values()) {
            if (indicator.getCode().equals(code)) {
                return indicator;
            }
        }
        return null;
    }
}
