package com.qmqb.imp.common.core.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 需求查询任务请求参数
 * Since:
 * 2022-11-21
 * Author:
 * huangchangrong
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StoryQueryDTO  extends BasePageDTO {


    /**
     * 年份
     */
//    private String year;

    /**
     * 月份
     */
//    private String month;


    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginDate;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 默认状态 all , draft , active , changed, closed
     */
    private String status;

    /**
     * 产品id
     */
    private Integer productId;
}
