package com.qmqb.imp.common.core.domain.dto;

import com.qmqb.imp.common.utils.DateUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 需求统计查询参数实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@Accessors(chain = true)
public class StoryStatQueryDTO {


    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    public static String beginToDate(String beginDate) {
        Date parseDate = DateUtils.parseDate(beginDate);
        if (parseDate == null) {
            return null;
        }
        Date toStart = DateUtils.dateToStart(parseDate);
        String toStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", toStart);
        return toStr;
    }

    public static String endToDate(String endDate) {
        Date parseDate = DateUtils.parseDate(endDate);
        if (parseDate == null) {
            return null;
        }
        Date toEnd = DateUtils.dateToEnd(parseDate);
        String toStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", toEnd);
        return toStr;
    }

}
