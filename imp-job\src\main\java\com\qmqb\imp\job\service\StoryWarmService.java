package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.StoryMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 需求预警
 * @date 2025/7/9 14:25
 */
@Component
@Slf4j
public class StoryWarmService {

    @Resource
    private StoryMapper storyMapper;

    @Resource
    private IMessageService messageService;

    @Resource
    private DingTalkConfig dingTalkConfig;

    @TraceId("需求用例预警")
    @XxlJob("storyCaseWarnJobHandler")
    public ReturnT<String> storyCaseWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行需求用例预警定时任务...");
            log.info("开始执行进行需求用例预警定时任务...");
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            StopWatch sw = new StopWatch();
            sw.start();
            LocalDate yesterday = LocalDate.now().plusDays(-1);
            Integer count = storyMapper.selectNotCaseStoryCount(yesterday.plusDays(-59), yesterday);
            if (count > 0) {
                send(dingTalkConfig.getJszxRobotUrl(), Constants.NO_CASE_STORY_COUNT, Collections.singletonMap("count", String.valueOf(count)));
            } else {
                send(dingTalkConfig.getJszxRobotUrl(), Constants.STORY_CASE_NORMAL, Collections.emptyMap());
            }
            sw.stop();
            XxlJobLogger.log("需求用例预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("需求用例预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("需求用例预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param template 模板
     * @param map      数据
     */
    private void send(String robotUrl, String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
