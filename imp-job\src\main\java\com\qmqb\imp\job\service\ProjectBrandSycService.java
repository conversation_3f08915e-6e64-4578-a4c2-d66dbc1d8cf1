package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.HttpStatus;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.DeptTypeEnum;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.WarnCodeEnum;
import com.qmqb.imp.common.enums.WarnTypeEnum;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.client.GitlabApiClient;
import com.qmqb.imp.system.domain.bo.WarnContentParamsBo;
import com.qmqb.imp.system.domain.bo.message.EmailMsgBo;
import com.qmqb.imp.system.domain.vo.BranchVo;
import com.qmqb.imp.system.domain.vo.ProjectVo;
import com.qmqb.imp.system.service.*;
import com.qmqb.imp.system.service.message.IMessageService;
import com.qmqb.imp.system.service.warn.ISendWarnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.UserApi;
import org.gitlab4j.api.models.Branch;
import org.gitlab4j.api.models.Project;
import org.gitlab4j.api.models.ProtectedBranch;
import org.gitlab4j.api.models.User;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目管理统计项目和分支
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectBrandSycService {
    private final GitlabApiClient gitlabApiClient;
    private final ISendWarnService sendWarnService;
    private final ISysUserService iSysUserService;
    private final IMessageService messageService;
    private final IProjectService iProjectService;
    private final IBranchService iBranchService;
    private final IScanProjectService scanProjectService;
    private final ISysDictTypeService sysDictTypeService;

    /**
     * 同步同步项目分支定时任务
     */
    @TraceId("同步项目分支")
    @XxlJob("syncProjectBrandJobHandler")
    public ReturnT<String> syncProjectBrandJobHandler(String param) {
        long batchNo = IdUtil.getId();

        try {
            // 初始化GitLab客户端
            GitLabApi gitLabApi = initializeGitLabClient(batchNo);

            // 清理现有数据
            Map<Long, ProjectVo> projectMap = cleanupExistingProjectData();
            cleanupExistingBranchData();

            // 执行数据同步
            StopWatch sw = new StopWatch();
            sw.start();
            performDataSync(gitLabApi, projectMap);
            sw.stop();

            log.info("同步项目分支记录耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            return handleSyncException(e, batchNo);
        }
    }

    /**
     * 初始化GitLab客户端
     */
    private GitLabApi initializeGitLabClient(long batchNo) throws Exception {
        try {
            return gitlabApiClient.build();
        } catch (Exception exception) {
            XxlJobLogger.log(exception);
            log.error("连接gitlab服务器异常:{}, 发送预警,数据预警批次号:{}", exception, batchNo);
            sendWarnService.sendWarn(batchNo, WarnCodeEnum.SYN_PROJECT_BRAND_WARN_P0.getCode(),
                Collections.singletonList(WarnContentParamsBo.builder().paramsCode("date").paramsValue(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD)).build()), null);
            throw exception;
        }
    }

    /**
     * 清理现有项目数据
     */
    private Map<Long, ProjectVo> cleanupExistingProjectData() {
        log.info("同步项目分支记录...");
        // 删除项目保证当天删除的分支后面统计的话为最新的
        List<ProjectVo> projectList = iProjectService.getByProjects();
        Map<Long, ProjectVo> projectMap = new HashMap<>(projectList.size());
        if (!CollectionUtils.isEmpty(projectList)) {
            projectMap = projectList.stream().collect(Collectors.toMap(ProjectVo::getPId, a -> a));
            List<Long> pIds = projectList.stream()
                .map(ProjectVo::getPId)
                .collect(Collectors.toList());
            iProjectService.batchsByProjectIds(pIds);
        }

        return projectMap;
    }

    /**
     * 清理现有分支数据
     */
    private void cleanupExistingBranchData() {
        List<BranchVo> branchs = iBranchService.getByBranchs();
        if (!CollectionUtils.isEmpty(branchs)) {
            List<Long> bIds = branchs.stream()
                .map(BranchVo::getBId)
                .collect(Collectors.toList());
            iBranchService.batchsByBranchIds(bIds);
        }
    }

    /**
     * 执行数据同步
     */
    private void performDataSync(GitLabApi gitLabApi, Map<Long, ProjectVo> projectMap) throws Exception {
        // 构建数据映射关系
        Map<Long, Map<String, BranchVo>> barnchsMap = iBranchService.getByBranchs().stream()
            .collect(Collectors.groupingBy(
                BranchVo::getBPid,
                Collectors.toMap(
                    BranchVo::getBName,
                    vo -> vo,
                    (existingValue, newValue) -> existingValue,
                    LinkedHashMap::new
                )
            ));
        Map<Long, ProjectVo> projectVoMap = iProjectService.getByProjects().stream()
            .collect(Collectors.toMap(ProjectVo::getPId, a -> a));
        UserApi userApi = gitLabApi.getUserApi();
        Map<Long, SysDept> userIdToDept = new HashMap<>(256);
        List<User> users = userApi.getUsers();
        if (CollUtil.isNotEmpty(users)) {
            Map<String, Long> userNameToUserId = users.stream()
                .collect(Collectors.toMap(User::getUsername, User::getId, (oldVal, newVal) -> oldVal));
            List<SysUser> sysUsers = iSysUserService.selectAllUser().stream()
                .filter(sysUser -> DeptTypeEnum.DEV.getCode().equals(sysUser.getDept().getDeptType()))
                .collect(Collectors.toList());
            for (SysUser sysUser : sysUsers) {
                String gitCommitterName = sysUser.getGitCommitterName();
                String gitAuthorName = sysUser.getGitAuthorName();
                if (userNameToUserId.get(gitCommitterName) != null || userNameToUserId.get(gitAuthorName) != null) {
                    Long userId = userNameToUserId.get(gitCommitterName) != null
                        ? userNameToUserId.get(gitCommitterName)
                        : userNameToUserId.get(gitAuthorName);
                    userIdToDept.put(userId, sysUser.getDept());
                }
            }
        }
        // 同步项目和分支数据
        List<com.qmqb.imp.system.domain.Project> pros = insertProjectAndBranch(gitLabApi, projectVoMap, projectMap, userIdToDept, barnchsMap);
        // 更新扫描项目表中的开发部门信息
        updateScanProjectDevDept(pros);
    }

    /**
     * 更新扫描项目表中的开发部门信息
     */
    private void updateScanProjectDevDept(List<com.qmqb.imp.system.domain.Project> pros) {
        Map<Long, String> devDeptInPid = new HashMap<>(pros.size());
        for (com.qmqb.imp.system.domain.Project project : pros) {
            devDeptInPid.put(project.getPId(), project.getPDevDept());
        }
        scanProjectService.updateDevDeptBatch(devDeptInPid);
    }

    /**
     * 处理同步异常
     */
    private ReturnT<String> handleSyncException(Exception e, long batchNo) {
        XxlJobLogger.log(e);
        log.error("发送预警,数据预警批次号:{},同步项目分支异常:", batchNo, e);

        List<SysUser> userList = iSysUserService.selectAllUser()
            .stream()
            .filter(user -> user.getDeptId().equals(1843829274434977794L))
            .collect(Collectors.toList());

        // 发送预警邮件
        userList.forEach(user -> this.handleErrorEmailSend(user, batchNo));

        return ReturnT.FAIL;
    }

    public List<com.qmqb.imp.system.domain.Project> insertProjectAndBranch(GitLabApi gitLabApi,Map<Long, ProjectVo> projectVoMap,Map<Long, ProjectVo> projectMap,Map<Long, SysDept> userIdToDept,Map<Long, Map<String, BranchVo>> barnchsMap) throws Exception {
        List<com.qmqb.imp.system.domain.Project> pros = new ArrayList<>();
        List<com.qmqb.imp.system.domain.Branch> bras = new ArrayList<>();
        //查询所有项目
        ProjectApi projectApi = gitLabApi.getProjectApi();
        List<Project> projects = projectApi.getProjects();
        for (Project p : projects) {
            // 同步项目分支
            ProjectVo projectVo = projectVoMap.get(p.getId());
            if (Objects.isNull(projectVo) && !p.getArchived()) {
                if (StrUtil.isNotBlank(p.getDescription()) && p.getDescription().contains("废弃")){
                    continue;
                }
                com.qmqb.imp.system.domain.Project pro = new com.qmqb.imp.system.domain.Project();
                pro.setPId(p.getId());
                pro.setPDesc(p.getDescription());
                pro.setPName(p.getName());
                pro.setPNamespace(p.getPathWithNamespace());
                pro.setPVisibility(p.getVisibility().toString());
                pro.setPWebUrl(p.getWebUrl());
                pro.setPCreatetime(p.getCreatedAt());
                pro.setPLastUpdateTime(new Date());
                pro.setPSyncDatetime(new Date());
                pro.setPBranchCount(0L);
                pro.setPHasMaster(0);
                pro.setPMasterProtected(0);
                //查询之前旧的值，要把旧的数据恢复
                ProjectVo pv = projectMap.get(p.getId());
                if (pv != null) {
                    pro.setPDevDept(pv.getPDevDept());
                    pro.setPTestDept(pv.getPTestDept());
                    pro.setPBroadBusiness(pv.getPBroadBusiness());
                    pro.setPNarrowBusiness(pv.getPNarrowBusiness());
                }
                //创建人来设置项目的负责开发组
                SysDept sysDept = userIdToDept.get(p.getCreatorId());
                boolean flag=(sysDept != null && (pro.getPDevDept() == null || pro.getPDevDept().isEmpty()));
                if (flag) {
                    pro.setPDevDept(String.valueOf(sysDept.getDeptId()));
                }else if (sysDept == null && StrUtil.isBlank(pro.getPDevDept())) {
                    //如果找不到组就全部设置为其他组，从字典管理查出其他组的id
                    String otherDeptId = sysDictTypeService.selectDictDataByType("code_dev_dept").stream().filter(sysDictData -> "其他组".equals(sysDictData.getDictLabel())).findFirst().orElse(null).getDictValue();
                    if (otherDeptId != null) {
                        pro.setPDevDept(otherDeptId);
                    }
                }
                // 同步项目分支
                List<Branch> branches = gitLabApi.getRepositoryApi().getBranches(p.getId());
                if (!CollectionUtils.isEmpty(branches)) {
                    pro.setPBranchCount((long) branches.size());
                    // 检查是否有master分支以及是否受保护
                    isBranchProtected(pro,gitLabApi,p.getId().toString(),branches);
                    for (Branch branch : branches) {
                        boolean isExist = false;
                        Map<String, BranchVo> branchById = barnchsMap.get(p.getId());
                        if (branchById != null && branchById.get(branch.getName()) != null) {
                            isExist = true;
                        }
                        if (!isExist) {
                            com.qmqb.imp.system.domain.Branch b = new com.qmqb.imp.system.domain.Branch();
                            b.setBPid(p.getId());
                            b.setBName(branch.getName());
                            b.setBDevpush(branch.getDevelopersCanPush().toString());
                            b.setBIsprotected(branch.getProtected().toString());
                            bras.add(b);
                        }
                    }
                }
                pros.add(pro);
            }
        }
        iProjectService.insertBatch(pros);
        iBranchService.insertBatch(bras);
        return pros;
       }

    private void handleErrorEmailSend(SysUser user, Long batchNo) {
        // 技术中心综合管理平台代码数据{date}同步异常，请及时处理
        List<WarnContentParamsBo> warnContentParamsBos = Collections.singletonList(WarnContentParamsBo.builder().paramsCode("date").paramsValue(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD)).build());
        Map<String, String> map = new HashMap<>(16);
        if (CollectionUtil.isNotEmpty(warnContentParamsBos)) {
            map = warnContentParamsBos.stream().collect(Collectors.toMap(WarnContentParamsBo::getParamsCode, WarnContentParamsBo::getParamsValue));
        }
        String content = StrUtil.format("技术中心综合管理平台代码数据{date}同步异常，请及时处理", map, true);
        EmailMsgBo emailMsgBo = EmailMsgBo.builder().from("技术中心综合管理平台").text(content).subject(WarnTypeEnum.getEnumsByValue(6).getName()).build();
        emailMsgBo.setChannelType(MessageChannelTypeEnum.EMAIL.getType());
        // 发送邮箱
        emailMsgBo.setTo(Collections.singletonList(user.getEmail()));
        try {
            messageService.sendBase(emailMsgBo);
        } catch (Exception exception) {
            log.error("同步代码调用消息中心发送消息异常，发送邮箱，批次号{}", batchNo);
            throw exception;
        }
    }

    /**
     * 检查指定分支是否受保护
     * @param gitLabApi gitlab Api
     * @param projectId 项目ID
     * @param pro 库
     * @param branches 分支
     * @return true如果分支受保护，false如果不受保护
     * @throws IOException 如果API调用失败
     */
    public void isBranchProtected(com.qmqb.imp.system.domain.Project pro,GitLabApi gitLabApi,String projectId, List<Branch> branches) throws Exception{
        boolean hasMaster = false;
        boolean masterProtected = false;
        for (Branch branch : branches) {
            if ("master".equals(branch.getName())) {
                hasMaster = true;
                // 检查master分支是否受保护
                try {
                    ProtectedBranch masterBranch = gitLabApi.getProtectedBranchesApi().getProtectedBranch(projectId, "master");
                    masterProtected = masterBranch != null;
                } catch (GitLabApiException e) {
                    if (e.getHttpStatus() != HttpStatus.NOT_FOUND) {
                        log.error("检查master分支保护状态时发生错误: ", e);
                        throw new ServiceException("检查master分支保护状态时发生错误: "+e.getMessage());
                    }
                }
                break;
            }
        }
        // 设置新字段值
        pro.setPHasMaster(hasMaster ? 1 : 0);
        pro.setPMasterProtected(masterProtected ? 1 : 0);
    }
}
