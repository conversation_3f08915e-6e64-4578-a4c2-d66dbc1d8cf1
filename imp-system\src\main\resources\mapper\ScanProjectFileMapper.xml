<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ScanProjectFileMapper">

    <resultMap type="com.qmqb.imp.system.domain.ScanProjectFile" id="ScanProjectFileResult">
        <result property="id" column="id"/>
        <result property="pId" column="p_id"/>
        <result property="scanVersion" column="scan_version"/>
        <result property="lastScanFlag" column="last_scan_flag"/>
        <result property="scanFileUrl" column="scan_file_url"/>
        <result property="blockerAmount" column="blocker_amount"/>
        <result property="criticalAmount" column="critical_amount"/>
        <result property="status" column="status"/>
        <result property="handleUser" column="handle_user"/>
        <result property="handleUserId" column="handle_user_id"/>
        <result property="handleTime" column="handle_time"/>
        <result property="handleRemark" column="handle_remark"/>
        <result property="assignUser" column="assign_user"/>
        <result property="assignUserId" column="assign_user_id"/>
        <result property="assignTime" column="assign_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="updatePreviousScanToOld">
        UPDATE tb_scan_project_file
        SET last_scan_flag = 0,
            update_time = NOW()
        WHERE last_scan_flag = 1
          AND del_flag = 0
          AND p_id = #{pid}
        <if test="scanFileUrls != null and scanFileUrls.size() > 0">
          AND scan_file_url IN
          <foreach collection="scanFileUrls" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
        </if>
    </update>

    <select id="selectByPidAndVersion" resultType="com.qmqb.imp.system.domain.vo.ScanProjectFileVo">
        SELECT *
        FROM tb_scan_project_file
        WHERE del_flag = 0
          AND p_id = #{pid}
          AND scan_version = #{scanVersion}
        ORDER BY scan_file_url
    </select>

    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM tb_scan_project_file
        WHERE del_flag = 0
          AND p_id = #{pid}
          AND last_scan_flag = 1
          AND status = #{status}
    </select>

    <select id="countByStatusAndDept" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM tb_scan_project_file spf
        LEFT JOIN tb_scan_project sp ON spf.p_id = sp.p_id
        WHERE spf.del_flag = 0
          AND spf.last_scan_flag = 1
          AND spf.status = #{status}
          AND sp.dev_dept = #{deptId}
          AND sp.last_scan_flag = 1
    </select>

    <select id="selectByAssignedUser" resultType="com.qmqb.imp.system.domain.vo.ScanProjectFileVo">
        SELECT spf.*, sp.scan_name
        FROM tb_scan_project_file spf
        LEFT JOIN tb_scan_project sp ON spf.p_id = sp.p_id AND sp.last_scan_flag = 1 AND sp.del_flag = 0
        WHERE spf.del_flag = 0
          AND spf.last_scan_flag = 1
          AND spf.handle_user_id = #{handleUserId}
          AND spf.assign_user_id = #{assignUserId}
          AND spf.status = '1'
        ORDER BY spf.assign_time DESC
    </select>

    <select id="selectLatestByPidAndScanFileUrl" resultMap="ScanProjectFileResult">
        SELECT *
        FROM tb_scan_project_file
        WHERE del_flag = 0
          AND p_id = #{pid}
          AND scan_file_url = #{scanFileUrl}
        ORDER BY scan_version DESC, create_time DESC
        LIMIT 1
    </select>

    <update id="updateFileStatusToHandled">
        UPDATE tb_scan_project_file
        SET status = '2',
            handle_time = #{handleTime},
            update_time = NOW()
        WHERE id = #{id}
    </update>


    <select id="selectFileStatisticsByProjectIds" resultType="com.qmqb.imp.system.domain.vo.ScanProjectVo">
        select
        p_id as pId,
        sum(case when status = '0' then 1 else 0 end) as unassignedAmount,
        sum(case when status = '1' then 1 else 0 end) as unhandledAmount,
        sum(case when status = '2' then 1 else 0 end) as handledAmount
        from tb_scan_project_file
        where p_id in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
        and (last_scan_flag = 1 or status = '2')
        and del_flag = 0
        group by p_id
    </select>

</mapper>
