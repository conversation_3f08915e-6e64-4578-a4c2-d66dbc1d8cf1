package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.system.domain.ReleaseRecord;
import com.qmqb.imp.system.domain.Story;
import com.qmqb.imp.system.domain.ZtAction;
import com.qmqb.imp.system.domain.ZtRelease;
import com.qmqb.imp.system.mapper.ReleaseRecordMapper;
import com.qmqb.imp.system.mapper.StoryMapper;
import com.qmqb.imp.system.mapper.ZtActionMapper;
import com.qmqb.imp.system.mapper.ZtReleaseMapper;
import com.qmqb.imp.system.service.IReleaseRecordNumberService;
import com.qmqb.imp.system.service.IStoryService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IZtActionService;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步发布版本记录定时任务
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncReleaseRecordService {

    private final ZtReleaseMapper ztReleaseMapper;
    private final IZtActionService ztActionService;
    private final ReleaseRecordMapper releaseRecordMapper;
    private final IStoryService storyService;
    private final ISysUserService sysUserService;
    private final IReleaseRecordNumberService releaseRecordNumberService;

    /**
     * 同步发布版本记录定时任务
     */
    @TraceId("同步发布版本记录")
    @XxlJob("syncReleaseRecordJobHandler")
    public ReturnT<String> syncReleaseRecordJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行同步发布版本记录定时任务...");
            log.info("开始执行同步发布版本记录定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            // 执行同步逻辑
            processSync();

            sw.stop();
            XxlJobLogger.log("同步发布版本记录定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("同步发布版本记录定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("同步发布版本记录定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 执行同步处理逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void processSync() {
        // 判断是否为首次同步，首次同步时候数据库中禅道同步数据为数量小于0
        boolean isFirstSync = (releaseRecordMapper.selectCount(
            new LambdaQueryWrapper<ReleaseRecord>()
                //来源禅道同步
                .eq(ReleaseRecord::getDataSource, "1")
        ) == 0);

        if (isFirstSync) {
            // 首次同步：全量同步（包括删除的数据）
            log.info("执行首次全量同步...");
            fullSync();
        } else {
            // 增量同步：根据最后更新时间同步变更数据
            log.info("执行增量同步...");
            incrementalSync();
        }
    }

    /**
     * 首次全量同步
     */
    private void fullSync() {
        log.info("开始执行首次全量同步...");

        // 获取技术总监和项目经理的禅道用户名
        List<String> allowedCreators = getAllowedCreators();
        log.info("获取到{}个允许的创建人: {}", allowedCreators.size(), allowedCreators);

        if (CollUtil.isEmpty(allowedCreators)) {
            log.warn("未找到技术总监或项目经理用户，跳过同步");
            return;
        }

        // 直接查询技术总监和项目经理创建的发布记录（包括已删除的）
        List<ZtRelease> ztReleases = ztReleaseMapper.selectSyncReleaseListByCreators(allowedCreators);

        log.info("从禅道获取到{}条发布记录（已过滤只保留技术总监和项目经理创建的）", ztReleases.size());

        if (CollUtil.isEmpty(ztReleases)) {
            log.info("未获取到符合条件的禅道发布数据");
            return;
        }

        // 批量处理同步数据
        processBatchSync(ztReleases, true);
        log.info("首次全量同步完成");
    }

    /**
     * 增量同步
     */
    private void incrementalSync() {
        log.info("开始执行增量同步...");

        // 获取技术总监和项目经理的禅道用户名
        List<String> allowedCreators = getAllowedCreators();
        log.info("获取到{}个允许的创建人: {}", allowedCreators.size(), allowedCreators);

        if (CollUtil.isEmpty(allowedCreators)) {
            log.warn("未找到技术总监或项目经理用户，跳过同步");
            return;
        }

        // 获取最后更新时间
        Date lastUpdateTime = getLastUpdateTime();
        log.info("最后更新时间: {}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, lastUpdateTime));

        // 查询有变动的发布记录ID（指定创建人）
        List<Integer> changedIds = ztReleaseMapper.selectChangedReleaseIdsByCreators(lastUpdateTime, allowedCreators);
        log.info("发现{}条有变动的发布记录（技术总监和项目经理创建的）", changedIds.size());

        // 查询已删除的发布记录ID（指定创建人）
        List<Integer> deletedIds = ztReleaseMapper.selectDeletedReleaseIdsByCreators(lastUpdateTime, allowedCreators);
        log.info("发现{}条已删除的发布记录（技术总监和项目经理创建的）", deletedIds.size());

        // 处理变更数据
        if (CollUtil.isNotEmpty(changedIds)) {
            List<ZtRelease> changedReleases = ztReleaseMapper.selectByIds(changedIds);
            processBatchSync(changedReleases, false);
        }

        // 处理删除数据
        if (CollUtil.isNotEmpty(deletedIds)) {
            processDeletedRecords(deletedIds);
        }

        log.info("增量同步完成");
    }

    /**
     * 获取最后更新时间
     */
    private Date getLastUpdateTime() {
        ReleaseRecord latestRecord = releaseRecordMapper.selectOne(
            new LambdaQueryWrapper<ReleaseRecord>()
                .eq(ReleaseRecord::getDataSource, "1")
                .orderByDesc(ReleaseRecord::getUpdatedTime)
                .last("LIMIT 1")
        );

        if (latestRecord != null) {
            return latestRecord.getUpdatedTime();
        }

        // 如果没有找到记录，使用一个较早的时间
        return DateUtil.parseDate("1970-01-01");
    }

            /**
     * 批量处理同步数据
     * @param ztReleases 禅道发布记录列表
     * @param isFullSync 是否为全量同步
     */
    private void processBatchSync(List<ZtRelease> ztReleases, boolean isFullSync) {
        log.info("开始批量处理同步数据，共{}条", ztReleases.size());

        // 获取现有的发布记录映射关系
        Map<Integer, ReleaseRecord> existingRecordsMap = getExistingRecordsMap(ztReleases, isFullSync);

        List<ReleaseRecord> toInsert = new ArrayList<>();
        List<ReleaseRecord> toUpdate = new ArrayList<>();

        // 获取当前日期和起始流水号 - 批量处理中预先获取
        Date now = new Date();
        String dateStr = DateUtils.parseDateToStr("yyyyMMdd", now);
        int nextSerial = releaseRecordNumberService.getBatchSerialNumber(dateStr, ztReleases.size());
        log.info("批量生成流水号，日期: {}, 起始序号: {}", dateStr, nextSerial);

        for (ZtRelease ztRelease : ztReleases) {
            try {
                boolean isUpdate = existingRecordsMap.containsKey(ztRelease.getId());
                ReleaseRecord existing = isUpdate ? existingRecordsMap.get(ztRelease.getId()) : null;

                // 根据是否是更新操作来转换记录
                ReleaseRecord releaseRecord = convertToReleaseRecord(ztRelease, !isUpdate, existing);

                if (isUpdate) {
                    // 更新现有记录 - 保留原有编号
                    releaseRecord.setId(existing.getId());
                    releaseRecord.setResultCode(existing.getResultCode());
                    releaseRecord.setCreatedBy(existing.getCreatedBy());
                    releaseRecord.setCreatedTime(existing.getCreatedTime());
                    releaseRecord.setUpdatedBy("system");
                    releaseRecord.setUpdatedTime(new Date());
                    toUpdate.add(releaseRecord);
                } else {
                    // 新增记录 - 生成递增的流水号
                    String resultCode = releaseRecordNumberService.generateResultCode(dateStr, nextSerial++);
                    releaseRecord.setResultCode(resultCode);
                    releaseRecord.setCreatedBy("system");
                    releaseRecord.setCreatedTime(new Date());
                    releaseRecord.setUpdatedBy("system");
                    releaseRecord.setUpdatedTime(new Date());
                    toInsert.add(releaseRecord);
                }
            } catch (Exception e) {
                log.error("处理发布记录失败, ID: {}, 错误: {}", ztRelease.getId(), e.getMessage(), e);
                throw new ServiceException(String.format("处理发布记录失败, ID: {%d}, 错误: {%s}", ztRelease.getId(), e.getMessage()));
            }
        }

        // 批量插入新记录
        if (CollUtil.isNotEmpty(toInsert)) {
            log.info("批量插入{}条新记录", toInsert.size());
            batchInsertRecords(toInsert);
        }

        // 批量更新现有记录
        if (CollUtil.isNotEmpty(toUpdate)) {
            log.info("批量更新{}条现有记录", toUpdate.size());
            batchUpdateRecords(toUpdate);
        }

        log.info("批量处理同步数据完成");
    }

    /**
     * 获取现有记录映射关系
     * @param ztReleases 禅道发布记录列表
     * @param isFullSync 是否为全量同步
     */
    private Map<Integer, ReleaseRecord> getExistingRecordsMap(List<ZtRelease> ztReleases, boolean isFullSync) {
        List<ReleaseRecord> existingRecords;

        if (isFullSync) {
            // 全量同步：查询所有禅道同步的记录
            existingRecords = releaseRecordMapper.selectList(
                new LambdaQueryWrapper<ReleaseRecord>()
                    .eq(ReleaseRecord::getDataSource, "1")
            );
        } else {
            // 增量同步：只查询当前批次涉及的记录
            List<Integer> sourceReleaseIds = ztReleases.stream()
                .map(ZtRelease::getId)
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(sourceReleaseIds)) {
                return new HashMap<>(0);
            }

            existingRecords = releaseRecordMapper.selectList(
                new LambdaQueryWrapper<ReleaseRecord>()
                    .eq(ReleaseRecord::getDataSource, "1")
                    .in(ReleaseRecord::getSourceReleaseId, sourceReleaseIds)
            );
        }

        return existingRecords.stream()
            .collect(Collectors.toMap(ReleaseRecord::getSourceReleaseId, record -> record));
    }

    /**
     * 处理删除的记录
     */
    private void processDeletedRecords(List<Integer> deletedIds) {
        log.info("开始处理{}条删除记录", deletedIds.size());

        // 将对应的记录标记为删除
        releaseRecordMapper.update(null,
            new LambdaUpdateWrapper<ReleaseRecord>()
                .eq(ReleaseRecord::getDataSource, "1")
                .in(ReleaseRecord::getSourceReleaseId, deletedIds)
                .set(ReleaseRecord::getDelFlag, 2)
                .set(ReleaseRecord::getUpdatedBy, "system")
                .set(ReleaseRecord::getUpdatedTime, new Date())
        );

        log.info("删除记录处理完成");
    }

    /**
     * 转换禅道发布记录为系统发布记录
     * @param ztRelease 禅道发布记录
     * @param isNewRecord 是否为新增记录
     * @param existingRecord 现有记录（更新时使用）
     */
    private ReleaseRecord convertToReleaseRecord(ZtRelease ztRelease, boolean isNewRecord, ReleaseRecord existingRecord) {
        ReleaseRecord record = new ReleaseRecord();

        // 基础字段映射（始终更新）
        record.setSourceReleaseId(ztRelease.getId());
        record.setDataSource("1");

        // 处理需求标题作为成果简介（始终更新）
        record.setResultSummary(buildResultSummary(ztRelease.getStories()));

        // 处理时间字段（始终更新）
        record.setStartTime(ztRelease.getDate() != null ? ztRelease.getDate() : ztRelease.getCreatedDate());
        record.setEndTime(getEndTime(ztRelease));

        // 设置项目经理（始终更新）- 直接使用创建人
        record.setProjectManager(ztRelease.getCreatedBy());

        // 设置删除标志（始终更新）
        record.setDelFlag("1".equals(ztRelease.getDeleted()) ? 2 : 0);

        if (isNewRecord) {
            // 新增记录：设置默认值，其他字段置空
            record.setResultType("");
            // 注意：不在这里设置resultCode，而是在processBatchSync方法中统一分配
            record.setBusinessCategoryMajor("");
            record.setBusinessCategoryMinor("");
            record.setDurationDays(0);
            record.setManpowerCost(0);
            record.setDevDepts("");
            record.setTestDepts("");
            record.setOtherDepts("");
        } else {
            // 更新记录：保留原有的业务字段
            if (existingRecord != null) {
                record.setResultType(existingRecord.getResultType());
                record.setBusinessCategoryMajor(existingRecord.getBusinessCategoryMajor());
                record.setBusinessCategoryMinor(existingRecord.getBusinessCategoryMinor());
                record.setDurationDays(existingRecord.getDurationDays());
                record.setManpowerCost(existingRecord.getManpowerCost());
                record.setDevDepts(existingRecord.getDevDepts());
                record.setTestDepts(existingRecord.getTestDepts());
                record.setOtherDepts(existingRecord.getOtherDepts());
            }
        }

        return record;
    }

    /**
     * 构建成果简介（关联需求标题）
     */
    private String buildResultSummary(String storiesStr) {
        if (StrUtil.isBlank(storiesStr)) {
            return "";
        }

        try {
            // 解析需求ID列表
            String[] storyIdArray = storiesStr.split(",");
            List<Integer> storyIds = Arrays.stream(storyIdArray)
                .filter(StrUtil::isNotBlank)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(storyIds)) {
                return "";
            }

            // 查询需求标题
            List<Story> stories = storyService.getTitleList(storyIds);

            if (CollUtil.isEmpty(stories)) {
                return "";
            }

            // 拼接需求标题，用换行符分隔
            return stories.stream()
                .map(Story::getTitle)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining("\n"));

        } catch (Exception e) {
            log.warn("解析需求标题失败, stories: {}, 错误: {}", storiesStr, e.getMessage());
            throw new ServiceException(String.format("解析需求标题失败, stories: {%s}, 错误: {%s}", storiesStr, e.getMessage()));
        }
    }

    /**
     * 获取结束时间
     * 优先获取zt_action表中action='published'的date时间，没有则使用zt_release.releasedDate
     */
    private Date getEndTime(ZtRelease ztRelease) {
        try {
            ZtAction publishAction = ztActionService.getReleaseActionById(ztRelease.getId());
            if (publishAction != null) {
                return publishAction.getDate();
            }

            // 没有发布操作记录，使用releasedDate
            return ztRelease.getReleasedDate();

        } catch (Exception e) {
            log.error("获取结束时间失败, releaseId: {}, 错误: {}", ztRelease.getId(), e.getMessage());
            throw new ServiceException(String.format("获取结束时间失败, releaseId: {%d}, 错误: {%s}", ztRelease.getId(), e.getMessage()));
        }
    }

    /**
     * 获取允许的创建人（技术总监和项目经理）
     */
    private List<String> getAllowedCreators() {
        try {
            // 查询所有用户
            List<SysUser> allUsers = sysUserService.selectAllUser();

            // 过滤出技术总监和项目经理
            List<String> allowedCreators = allUsers.stream()
                .filter(user -> user.isJszxAdmin() || user.isProjectManager())
                .map(SysUser::getZtUserName)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

            log.info("找到{}个技术总监和项目经理用户", allowedCreators.size());
            return allowedCreators;

        } catch (Exception e) {
            log.error("获取允许的创建人失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量插入记录
     */
    private void batchInsertRecords(List<ReleaseRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        boolean success = releaseRecordMapper.insertBatch(records);
        if (success) {
            log.info("批量插入{}条记录成功", records.size());
        } else {
            throw new RuntimeException("批量插入记录失败，记录数: " + records.size());
        }
    }

    /**
     * 批量更新记录
     */
    private void batchUpdateRecords(List<ReleaseRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        boolean success = releaseRecordMapper.updateBatchById(records);
        if (success) {
            log.info("批量更新{}条记录成功", records.size());
        } else {
            throw new RuntimeException("批量更新记录失败，记录数: " + records.size());
        }
    }
}
