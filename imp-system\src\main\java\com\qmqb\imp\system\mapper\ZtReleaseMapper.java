package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.ZtRelease;
import com.qmqb.imp.system.domain.vo.ZtReleaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 禅道发布表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@DS(DataSource.ZENTAO)
public interface ZtReleaseMapper extends BaseMapperPlus<ZtReleaseMapper, ZtRelease, ZtReleaseVo> {

    /**
     * 查询需要同步的发布记录
     * 获取所有发布记录（包括已删除的）
     * @return 发布记录列表
     */
    List<ZtRelease> selectSyncReleaseList();

    /**
     * 查询需要同步的发布记录（按创建人过滤）
     * 获取指定创建人的发布记录（包括已删除的）
     * @param creators 创建人列表
     * @return 发布记录列表
     */
    List<ZtRelease> selectSyncReleaseListByCreators(@Param("creators") List<String> creators);

    /**
     * 根据更新时间查询有变动的发布记录ID
     * 通过zt_action表查询指定时间后有变动的release
     * @param afterTime 指定时间
     * @return 有变动的发布记录ID列表
     */
    List<Integer> selectChangedReleaseIds(@Param("afterTime") Date afterTime);

    /**
     * 根据更新时间和创建人查询有变动的发布记录ID
     * 通过zt_action表查询指定时间后有变动的release（按创建人过滤）
     * @param afterTime 指定时间
     * @param creators 创建人列表
     * @return 有变动的发布记录ID列表
     */
    List<Integer> selectChangedReleaseIdsByCreators(@Param("afterTime") Date afterTime, @Param("creators") List<String> creators);

    /**
     * 查询删除的发布记录ID
     * 通过zt_action表查询指定时间后被删除的release
     * @param afterTime 指定时间
     * @return 被删除的发布记录ID列表
     */
    List<Integer> selectDeletedReleaseIds(@Param("afterTime") Date afterTime);

    /**
     * 根据更新时间和创建人查询删除的发布记录ID
     * 通过zt_action表查询指定时间后被删除的release（按创建人过滤）
     * @param afterTime 指定时间
     * @param creators 创建人列表
     * @return 被删除的发布记录ID列表
     */
    List<Integer> selectDeletedReleaseIdsByCreators(@Param("afterTime") Date afterTime, @Param("creators") List<String> creators);

    /**
     * 根据ID列表查询发布记录
     * @param ids 发布记录ID列表
     * @return 发布记录列表
     */
    List<ZtRelease> selectByIds(@Param("ids") List<Integer> ids);

} 