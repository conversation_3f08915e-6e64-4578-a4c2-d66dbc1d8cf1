package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PerformanceFeedbackAuditStatusEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 绩效反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@RequiredArgsConstructor
@Service
public class PerformanceFeedbackServiceImpl extends ServiceImpl<PerformanceFeedbackMapper, PerformanceFeedback> implements IPerformanceFeedbackService {

    private final PerformanceFeedbackMapper baseMapper;
    private final ISysUserService sysUserService;
    private final PerformanceFeedbackMainMapper performanceFeedbackMainMapper;

    /**
     * 查询绩效反馈
     */
    @Override
    public PerformanceFeedbackVo queryById(Long id) {
        PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
        bo.setId(id);
        List<PerformanceFeedbackVo> performanceFeedbackVos = baseMapper.selectVoJoinMainList(bo);
        if (performanceFeedbackVos.isEmpty()) {
            return null;
        }
        return performanceFeedbackVos.get(0);
    }

    /**
     * 查询绩效反馈列表（分页）
     */
    @Override
    public TableDataInfo<PerformanceFeedbackVo> queryPageList(PerformanceFeedbackBo bo, PageQuery pageQuery) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (ObjUtil.isEmpty(pageQuery.getOrderByColumn())){
            // 默认按事件结束时间降序排序
            pageQuery.setOrderByColumn("eventEndTime");
            pageQuery.setIsAsc("desc");
        }

        // 权限过滤：如果不是管理员或技术总监，只能查看自己所在组的数据
        if (currentUser != null && currentUser.getRoles() != null) {
            if (!currentUser.isAdmin() && !currentUser.isJszxAdmin() && !currentUser.isProjectManager()) {
                bo.setGroupId(currentUser.getDeptId());
            }
            if (currentUser.isProjectManager()){
                // 如果是项目经理，查询自己审核的数据
                bo.setProjectManagerAuditor(currentUser.getUserName());
                bo.setIsProjectManager(Boolean.TRUE);
            }
        }
        Page<PerformanceFeedbackVo> result = baseMapper.selectVoJoinMainPage(pageQuery.build(), bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效反馈列表
     */
    @Override
    public List<PerformanceFeedbackVo> queryList(PerformanceFeedbackBo bo) {
        return baseMapper.selectVoJoinMainList(bo);
    }

    @Override
    public List<PerformanceFeedback> queryApprovalList(PerformanceFeedbackBo bo) {
        return baseMapper.selectVoApprovalJoinMainList(bo);
    }

    /**
     * 新增绩效反馈
     */
    @Override
    public Boolean insertByBo(PerformanceFeedbackBo bo) {
        PerformanceFeedback add = BeanUtil.toBean(bo, PerformanceFeedback.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改绩效反馈
     */
    @Override
    public Boolean updateByBo(PerformanceFeedbackBo bo) {
        PerformanceFeedback update = BeanUtil.toBean(bo, PerformanceFeedback.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        // 自动提交主表
        if (flag && bo.getMainFeedbackId() != null) {
            PerformanceFeedbackMain main = performanceFeedbackMainMapper.selectById(bo.getMainFeedbackId());
            if (main != null) {
                main.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode());
                main.setSubmitter(LoginHelper.getUsername());
                main.setSubmitTime(new java.util.Date());
                // 审核状态默认设置为未审核
                main.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
                main.setProjectManagerRemark("");
                main.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
                main.setFinalRemark("");
                performanceFeedbackMainMapper.updateById(main);
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceFeedback entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效反馈
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据员工昵称、年份、月份删除绩效反馈记录
     */
    @Override
    public void removeByNickNameAndMonth(String nickName, Integer year, Integer month) {
        LambdaQueryWrapper<PerformanceFeedback> wrapper = Wrappers.lambdaQuery(PerformanceFeedback.class)
            .eq(PerformanceFeedback::getNickName, nickName)
            .eq(PerformanceFeedback::getYear, year)
            .eq(PerformanceFeedback::getMonth, month);
        baseMapper.delete(wrapper);
    }

    @Override
    public void removeByMainIds(List<String> mainIds) {
        if (mainIds == null || mainIds.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<PerformanceFeedback> wrapper = Wrappers.lambdaQuery(PerformanceFeedback.class)
            .in(PerformanceFeedback::getMainFeedbackId, mainIds);
        baseMapper.delete(wrapper);
    }

    /**
     * 根据年份、月份删除绩效反馈记录
     */
    @Override
    public void removeByYearAndMonth(Integer year, Integer month) {
        LambdaQueryWrapper<PerformanceFeedback> wrapper = Wrappers.lambdaQuery(PerformanceFeedback.class)
            .eq(PerformanceFeedback::getYear, year)
            .eq(PerformanceFeedback::getMonth, month);
        baseMapper.delete(wrapper);
    }

    /**
     * 根据年份、月份查询绩效反馈记录
     */
    @Override
    public List<PerformanceFeedback> getByYearAndMonth(Integer year, Integer month) {
        // 只查询最终审核通过的记录
        PerformanceFeedbackBo feedbackBo = new PerformanceFeedbackBo();
        feedbackBo.setYear(year);
        feedbackBo.setMonth(month);
        feedbackBo.setFinalAudit(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
        return baseMapper.selectVoApprovalJoinMainList(feedbackBo);
    }

    /**
     * 根据员工昵称、年份、月份查询绩效反馈记录
     */
    @Override
    public List<PerformanceFeedback> getByNickNameAndYearMonth(String nickName, Integer year, Integer month) {
        LambdaQueryWrapper<PerformanceFeedback> wrapper = Wrappers.lambdaQuery(PerformanceFeedback.class)
            .eq(PerformanceFeedback::getNickName, nickName)
            .eq(PerformanceFeedback::getYear, year)
            .eq(PerformanceFeedback::getMonth, month);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 批量保存绩效反馈记录
     */
    @Override
    public void saveBatch(List<PerformanceFeedback> feedbackList) {
        if (feedbackList != null && !feedbackList.isEmpty()) {
            super.saveBatch(feedbackList);
        }
    }

    /**
     * 根据日期前缀查询最大反馈编码
     *
     * @param datePrefix 日期前缀，格式：FKyyyyMMdd
     * @return 最大反馈编码，如果没有记录则返回null
     */
    @Override
    public String getMaxFeedbackCodeByDatePrefix(String datePrefix) {
        return null;
    }

    /**
     * 根据主表编码查询最大明细反馈编码
     *
     * @param mainFeedbackCode 主表编码
     * @return 最大明细反馈编码，如果没有记录则返回null
     */
    @Override
    public String getMaxFeedbackCodeByMainFeedbackCode(String mainFeedbackCode) {
        return null;
    }

    @Override
    public List<PerformanceFeedback> getMainBySecondaryIndicator(String nickName, Integer year, Integer month, String secondaryIndicator) {
        LambdaQueryWrapper<PerformanceFeedback> wrapper = Wrappers.lambdaQuery(PerformanceFeedback.class)
            .eq(PerformanceFeedback::getNickName, nickName)
            .eq(PerformanceFeedback::getYear, year)
            .eq(PerformanceFeedback::getMonth, month)
            .eq(StringUtils.isNotBlank(secondaryIndicator), PerformanceFeedback::getSecondaryIndicator, secondaryIndicator);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PerformanceFeedback> buildQueryWrapper(PerformanceFeedbackBo bo) {
        LambdaQueryWrapper<PerformanceFeedback> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, PerformanceFeedback::getId, bo.getId());
        lqw.eq(bo.getMainFeedbackId() != null, PerformanceFeedback::getMainFeedbackId, bo.getMainFeedbackId());
        lqw.like(StringUtils.isNotBlank(bo.getPrimaryIndicator()), PerformanceFeedback::getPrimaryIndicator, bo.getPrimaryIndicator());
        lqw.like(StringUtils.isNotBlank(bo.getSecondaryIndicator()), PerformanceFeedback::getSecondaryIndicator, bo.getSecondaryIndicator());
        lqw.like(StringUtils.isNotBlank(bo.getEventTitle()), PerformanceFeedback::getEventTitle, bo.getEventTitle());
        lqw.like(StringUtils.isNotBlank(bo.getEventDetail()), PerformanceFeedback::getEventDetail, bo.getEventDetail());
        lqw.eq(StringUtils.isNotBlank(bo.getRecommendedLevel()), PerformanceFeedback::getRecommendedLevel, bo.getRecommendedLevel());
        lqw.like(StringUtils.isNotBlank(bo.getRecommendedReason()), PerformanceFeedback::getRecommendedReason, bo.getRecommendedReason());
        lqw.eq(bo.getGroupId() != null, PerformanceFeedback::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), PerformanceFeedback::getGroupName, bo.getGroupName());
        return lqw;
    }

    /**
     * 根据主表ID集合批量删除绩效反馈明细
     *
     * @param mainFeedbackIds 主表ID集合
     * @return 删除条数
     */
    @Override
    public int deleteByMainFeedbackIds(List<Long> mainFeedbackIds) {
        if (mainFeedbackIds == null || mainFeedbackIds.isEmpty()) {
            return 0;
        }
        return baseMapper.deleteByMainFeedbackIds(mainFeedbackIds);
    }
}
