package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.SlowQueryDbList;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.domain.enums.SlowQueryProcessStatusEnum;
import com.qmqb.imp.system.mapper.SlowMonthStatsMapper;
import com.qmqb.imp.system.mapper.SlowQueryDbListMapper;
import com.qmqb.imp.system.mapper.SlowQueryStatsMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 慢sql问题通知定时器
 * @date 2025/6/27 11:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SlowSqlWarnService {

    private final SlowQueryDbListMapper slowQueryDbListMapper;
    private final SlowMonthStatsMapper slowMonthStatsMapper;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;
    private final SlowQueryStatsMapper slowQueryStatsMapper;
    /**
     * 完成所有慢SQL通知
     */
    public static final String FINISH = "暂时没有待处理的慢SQL，太棒啦！";

    @TraceId("慢sql问题通知定时任务")
    @XxlJob("slowSqlWarnJobHandler")
    public ReturnT<String> slowSqlWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("慢sql问题通知定时任务...");
            log.info("慢sql问题通知定时任务");
            val sw = new StopWatch();
            sw.start();
            // 统计各库的慢sql问题数据
            List<String> warnDbList = new ArrayList<>();
            for (SlowQueryDbList slowQueryDbList : slowQueryDbListMapper.selectList()) {
                // 统计该组的未指派数据（status=0）
                Long unassignedCount = countProblemsByStatusAndDb(slowQueryDbList.getDbCode(), 0, null);
                // 统计该组的已指派未处理数据（status=1）
                Long assignedUnhandledCount = countProblemsByStatusAndDb(slowQueryDbList.getDbCode(), 1, null);
                // 只统计有慢sql的库（两个数据都为0的组不统计）
                if (unassignedCount > 0 || assignedUnhandledCount > 0) {
                    String warnInfo = String.format("%s   已指派未处理：%d条    未指派：%d条",
                        slowQueryDbList.getDbName(), assignedUnhandledCount, unassignedCount);
                    warnDbList.add(warnInfo);
                }
            }
            // 发送通知
            if (CollectionUtil.isNotEmpty(warnDbList)) {
                Map<String, String> map = new HashMap<>(16);
                map.put("groupList", String.join("\n", warnDbList));
                send(Constants.SLOW_SQL_WARN_TAG, map);
                XxlJobLogger.log("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
                log.info("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
            } else {
                log.info("所有组慢sql问题均已处理完毕，无需发送通知");
                XxlJobLogger.log("所有组慢sql问题均已处理完毕，无需发送通知");
            }
            sw.stop();
            XxlJobLogger.log("慢sql问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("慢sql问题通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("慢sql问题通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    @TraceId("慢sql问题未指派通知定时任务")
    @XxlJob("slowSqlNotAssignWarnJobHandler")
    public ReturnT<String> slowSqlNotAssignWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("慢sql问题未指派通知定时任务...");
            log.info("慢sql问题未指派通知定时任务");
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            val sw = new StopWatch();
            sw.start();
            // 统计各库的慢sql问题数据
            List<String> warnDbList = new ArrayList<>();
            Date sevenDayBefore = DateUtils.plusDay(new Date(), -7);
            Set<String> sqlHashSet = slowQueryStatsMapper.selectList(new LambdaQueryWrapper<SlowQueryStats>()
                    .ge(SlowQueryStats::getLastExecutionStartTime, sevenDayBefore))
                .stream().map(SlowQueryStats::getSqlHash).collect(Collectors.toSet());
            Map<String, List<SlowMonthStats>> statsMap = sqlHashSet.isEmpty() ? Collections.emptyMap() : slowMonthStatsMapper
                .selectList(new LambdaQueryWrapper<SlowMonthStats>()
                    .in(SlowMonthStats::getSqlHash, sqlHashSet)
                    .in(SlowMonthStats::getStatus, SlowQueryProcessStatusEnum.NOT_ASSIGN.getCode(), SlowQueryProcessStatusEnum.ASSIGN_NOT_PROCESS.getCode()))
                .stream().collect(Collectors.groupingBy(SlowMonthStats::getDbName));
            for (SlowQueryDbList slowQueryDbList : slowQueryDbListMapper.selectList()) {
                List<SlowMonthStats> list = statsMap.getOrDefault(slowQueryDbList.getDbCode(), Collections.emptyList());
                // 统计该组的未指派数据（status=0）
                long notAssignedCount = list.stream().filter(item -> SlowQueryProcessStatusEnum.NOT_ASSIGN.getCode().equals(item.getStatus())).count();
                // 统计该组的已指派未处理数据（status=1）
                long assignedNotHandledCount = list.stream().filter(item -> SlowQueryProcessStatusEnum.ASSIGN_NOT_PROCESS.getCode().equals(item.getStatus())).count();
                // 只统计有慢sql的库（两个数据都为0的组不统计）
                if (notAssignedCount > 0 || assignedNotHandledCount > 0) {
                    String warnInfo = String.format("【%s】出现未指派的慢SQL条数为【%s】条，已指派未处理有【%s】条，请组长抓紧指派或处理人抓紧处理",
                        slowQueryDbList.getDbName(), notAssignedCount, assignedNotHandledCount);
                    warnDbList.add(warnInfo);
                }
            }
            // 发送通知
            if (CollectionUtil.isNotEmpty(warnDbList)) {
                Map<String, String> map = new HashMap<>(16);
                map.put("content", String.join("\n", warnDbList));
                send(Constants.SLOW_SQL_NOT_ASSIGN_WARN_TAG, map);
                XxlJobLogger.log("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
                log.info("发送慢sql问题通知，涉及库数：{}", warnDbList.size());
            } else {
                Map<String, String> map = new HashMap<>(16);
                map.put("content", FINISH);
                send(Constants.SLOW_SQL_NOT_ASSIGN_WARN_TAG, map);
                log.info("所有组慢sql问题均已处理完毕");
                XxlJobLogger.log("所有组慢sql问题均已处理完毕");
            }
            sw.stop();
            XxlJobLogger.log("慢sql问题未指派通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("慢sql问题未指派通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("慢sql问题未指派通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 统计数据库名和状态的慢sql数量
     *
     * @param dbName    数据库名
     * @param status    状态（0未指派，1已指派未处理，2已指派已处理）
     * @param startTime 开始时间
     * @return 慢sql数量
     */
    private Long countProblemsByStatusAndDb(String dbName, Integer status, Date startTime) {
        try {
            return slowMonthStatsMapper.selectCount(new LambdaQueryWrapper<SlowMonthStats>()
                .eq(SlowMonthStats::getDbName, dbName)
                .eq(SlowMonthStats::getStatus, status)
                .ge(startTime != null, SlowMonthStats::getCreateTime, startTime));
        } catch (Exception e) {
            log.error("统计部门{}状态{}的问题数量异常", dbName, status, e);
            return 0L;
        }
    }

    /**
     * 发送预警
     *
     * @param template 模板
     * @param map      数据
     */
    private void send(String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(dingTalkConfig.getJszxRobotUrl())
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
