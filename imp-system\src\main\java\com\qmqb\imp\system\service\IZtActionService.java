package com.qmqb.imp.system.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.system.domain.ZtAction;
import com.qmqb.imp.system.domain.vo.ActionVO;
import com.qmqb.imp.system.domain.vo.ZtActionVo;
import com.qmqb.imp.system.domain.bo.ZtActionBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@DS(DataSource.ZENTAO)
public interface IZtActionService {

    /**
     * 根据ID查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】ID
     * @return 【请填写功能名称】详细信息
     */
    ZtActionVo queryById(Integer id);

    /**
     * 根据条件分页查询【请填写功能名称】列表
     *
     * @param bo 查询条件对象
     * @param pageQuery 分页查询对象
     * @return 分页查询结果
     */
    TableDataInfo<ZtActionVo> queryPageList(ZtActionBo bo, PageQuery pageQuery);

    /**
     * 根据条件查询【请填写功能名称】列表
     *
     * @param bo 查询条件对象
     * @return 查询结果列表
     */
    List<ZtActionVo> queryList(ZtActionBo bo);

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 新增对象
     * @return 新增结果
     */
    Boolean insertByBo(ZtActionBo bo);

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 修改对象
     * @return 修改结果
     */
    Boolean updateByBo(ZtActionBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids 需要删除的ID集合
     * @param isValid 是否校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);


    /**
     * 根据对象ID和操作列表查询【请填写功能名称】
     *
     * @param objectIdList 对象ID列表
     * @param actionList 操作列表
     * @return 查询结果列表
     */
    List<ActionVO> queryByObjectIdAndAction(List<Integer> objectIdList, List<String> actionList);

    /**
     * 查询任务操作记录
     *
     * @param objectId 对象ID
     * @param action 操作
     * @return 查询结果
     */
    ZtActionVo queryByObjectIdAndAction(Integer objectId, String action);

    /**
     * 根据编辑人和时间查询比endTime早编辑的最后一次编辑记录
     *
     * @param objectId 对象ID
     * @param actor    编辑人
     * @param endTime  结束时间
     * @param objectType 对象类型
     * @return 查询结果
     */
    ActionVO queryByObjectIdAndActor(Integer objectId, String actor, Date endTime,String objectType);

    /**
     * 获取任务的所有动作记录
     * @param taskId 任务ID
     * @return 按时间排序的所有动作记录
     */
    List<ZtAction> getTaskActions(Integer taskId);

    /**
     * 根据对象ID和类型获取动作记录
     *
     * @param objId  对象ID
     * @param action 动作类型
     * @return 动作记录列表
     */
    List<ZtAction> getActionsByObjIdAndType(Integer objId, String action);

    /**
     * 根据发布记录ID和动作类型获取动作记录
     *
     * @param releaseId 发布记录ID
     * @return 动作记录列表
     */
    ZtAction getReleaseActionById(Integer releaseId);
}
